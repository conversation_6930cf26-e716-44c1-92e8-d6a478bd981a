#include "config.h"
#include "fabric.h"
#include "fab_common.h"
#include <sys/time.h>


#if defined(TIRPC_EPOLL)
		struct {
			struct epoll_event event_recv;
			struct epoll_event event_send;
			struct xdr_ioq *xioq_send;
		} epoll;
#endif



static int wait_destory_ep_num = 0;
int wait_destory_ep_num_max = 0;
int wait_destory_ep_num_max_abort = 0;
struct fabric_class *fab_clas = NULL;
int cur_eps_count = 0;
static int max_eps_connections = 0;
bool enable_debug_cq = false;
bool enable_cpu_affinity = false;

void cq_thread_callback(void *context);
int fabric_workpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx)
{
	int ret = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() NFS/FABRIC  submit fab_cals %p, callback %p, ctx %p.", __func__, fab_clas, handle_callback, ctx);
	ret = thpool_add_job_nolock(fab_clas->work_thpool, handle_callback, ctx);
	return ret;
}
int fabric_rpcpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx)
{
	int ret = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() NFS/FABRIC  submit fab_cals %p, callback %p, ctx %p.", __func__, fab_clas, handle_callback, ctx);
	ret = thpool_add_job_nolock(fab_clas->rpc_thpool, handle_callback, ctx);
	return ret;
}
int fabric_rxpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx)
{
	int ret = 0;
	if(fab_clas->thr_nolock == true) {
		//ret = thpool_add_job_nolock(fab_clas->cq_thpool, handle_callback, ctx);
		ret = thpool_add_job_nolock(fab_clas->rpc_thpool, handle_callback, ctx);
	} else {
		ret = thpool_add_job(fab_clas->rx_thpool, handle_callback, ctx);
	}
	return ret;
}
int fabric_txpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx)
{
	int ret = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() NFS/FABRIC  submit fab_cals %p, callback %p, ctx %p.", __func__, fab_clas, handle_callback, ctx);
	if (fab_clas->thr_nolock == true) {
		if (fab_clas->thr_work_send == false) {
			ret = thpool_add_job_nolock(fab_clas->cq_thpool, handle_callback, ctx);
		} else {
			handle_callback(ctx);
		}
	} else {
		ret = thpool_add_reply_job(fab_clas->tx_thpool, handle_callback, ctx);
	}
	return ret;
}
int fabric_cqpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx, int thrid)
{
	int ret = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() NFS/FABRIC  submit fab_cals %p, callback %p, ctx %p.", __func__, fab_clas, handle_callback, ctx);
	if (fab_clas->thr_nolock == true) {
		//if (((struct rpc_rdma_attr *)fab_clas->attr)->enable_workth_send == false) {
		if (fab_clas->thr_work_send == false) {
			//ret = thpool_add_job_nolock(fab_clas->cq_thpool, handle_callback, ctx);
			ret = thpool_add_job_nolock_withthrid(fab_clas->cq_thpool, handle_callback, ctx, thrid);
		} else {
			handle_callback(ctx);
		}
	} else {
		ret = thpool_add_reply_job(fab_clas->tx_thpool, handle_callback, ctx);
	}
	return ret;
}

/*
	submit to qos thread lock
*/
int thpool_qos_nolock_submit(void (*handle_callback)(void *), void *ctx)
{
	int ret = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() NFS/FABRIC  submit fab_cals %p, callback %p, ctx %p.", __func__, fab_clas, handle_callback, ctx);
	ret = thpool_add_reply_job(fab_clas->qos_thpool, handle_callback, ctx);
	return ret;
}

static inline void fabric_add_ep_list(fabric_class *fab_clas, fabric_endpoint *fab_endp) {
	static int index = 0;
	static unsigned int cur_session_id = 0;
	fab_endp->thrid = index;
	fab_clas->eps_list[index].ep_count ++;
	cur_eps_count ++;
	probe_count_inc(PROBE_COUNT_EPS);
	index ++;
	if (index >= fab_clas->attr->thread_cq_num)
		index = 0;

	cur_session_id ++;
	fab_endp->session_id = cur_session_id;
	now(&fab_endp->online_time);

	return;
}
static inline void fabric_del_ep_list(fabric_endpoint *fab_ep) {
	fab_clas->eps_list[fab_ep->thrid].ep_count --;
	cur_eps_count --;
	probe_count_dec(PROBE_COUNT_EPS);
	assert(cur_eps_count >= 0);
	return;
}

static inline int fabric_get_ep_num(fabric_class *fab_clas, int thr_index) {

	return fab_clas->eps_list[thr_index].ep_count;
}
/*static int init_node_cpus(char* node)
{
    //根据ip地址 获取网卡名
    struct ifaddrs *ifaddr, *ifa;
    char* interface = NULL;
    if (getifaddrs(&ifaddr) == -1) {
        perror("getifaddrs");
        return errno;
    }

    for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == NULL || ifa->ifa_addr->sa_family != AF_INET) {
            continue;
        }

        struct sockaddr_in* addr = (struct sockaddr_in*)ifa->ifa_addr;
        if (strcmp(inet_ntoa(addr->sin_addr), node) == 0) {
            interface = strdup(ifa->ifa_name);
            break;
        }
    }
    freeifaddrs(ifaddr);

    if (interface == NULL) {
        perror("get interface failed");
        return -errno;
    }

    //根据网卡名转换为mlnx
    FILE *p;
    char command[256];
    memset(command, 0, sizeof(command));
    char name[256];
    snprintf(command, sizeof(command), "ibdev2netdev | grep %s | awk '{print $1}'", interface);
    p = popen(command, "r");
    if (p == NULL) {
        perror("fopen");
        return -errno;
    }
    if (fgets(name, sizeof(name), p) == NULL) {
        perror("fgets");
        pclose(p);
        return -errno;
    }
    pclose(p);

    char* mlnx_name = strtok(name, " ");
    char* n =strchr(mlnx_name, '\n');
    if (n != NULL) {
        *n = '\0';
    }

    //获取设备numa node
    FILE *fp;
    char device_path[256];
    snprintf(device_path, sizeof(device_path), "/sys/class/infiniband/%s/device/numa_node", mlnx_name);
    fp = fopen(device_path, "r");
    if (fp == NULL) {
        perror("fopen");
	return -errno;
    }

    fscanf(fp, "%d", &numa_node);
    fclose(fp);

    //绑到numa 0&7
    if (numa_node <= 3) {
        numa_node = 3;
    } else {
        numa_node = 4;
    }

    //获取nuam上的cpus
    if (numa_available()) {
        struct bitmask *cpumask = numa_allocate_cpumask();
        if (numa_node_to_cpus(numa_node, cpumask)) {
            printf(" failed to get cpu for numa: %d\n", numa_node);
            return -errno;
        }
        int numa_cpus = 0;
        int cpu_num = numa_num_possible_cpus();

        for (int cpu = 0 ; cpu < cpu_num; cpu++) {
            if (numa_bitmask_isbitset(cpumask, cpu)) {
               node_cpus[numa_cpus] = cpu;
               numa_cpus ++;
               printf(" binding cpu: %d\n", cpu);
            }
        }
    } else {
        printf(" numa unavailable, use numactl: %d\n", numa_node);

	    FILE *np;
        char numa_command[256];
        memset(numa_command, 0, sizeof(numa_command));
        char cpus[256];
        snprintf(numa_command, sizeof(numa_command), "numactl -H | grep 'node %d cpus' | cut -d ':' -f 2", numa_node);
        np = popen(numa_command, "r");
        if (np == NULL) {
            perror("fopen");
            return -errno;
        }
        if (fgets(cpus, sizeof(cpus), np) == NULL) {
            perror("fgets");
            pclose(np);
            return -errno;
        }
        pclose(np);
        int numa_cpus = 0;
        char *token = strtok(cpus, " ");
	    while (token != NULL) {
	        if (sscanf(token, "%d", &node_cpus[numa_cpus]) == 1) {
	            numa_cpus ++;
	        }
	        token = strtok(NULL, " ");
	    }

    }
    //使用同一个物理核的core
    node_cpus[3] = node_cpus[6];
    node_cpus[4] = node_cpus[7];
    node_cpus[5] = node_cpus[8];
    return 0;
}*/
static int netdev_to_ibdev(const char* netdev, char* ibdev)
{
    //根据网卡名转换为mlnx
    FILE *p;
    char command[256];
    memset(command, 0, sizeof(command));
    char name[256];
    snprintf(command, sizeof(command), "ibdev2netdev | grep %s | awk '{print $1}'", netdev);
    p = popen(command, "r");
    if (p == NULL) {
        perror("fopen");
        return -errno;
    }
    if (fgets(name, sizeof(name), p) == NULL) {
        perror("fgets");
        pclose(p);
        return -errno;
    }
    pclose(p);
    int size= strlen(name);
    name[size-1] = '\0';
    strncpy(ibdev, name, 256);
    return 0;
}

int get_netdev_by_addr(const char* node, char* interface)
{
    //1、获取全部网卡
    struct ifaddrs *ifaddr, *ifa;
    if (getifaddrs(&ifaddr) == -1) {
        perror("getifaddrs");
        return errno;
    }
	//2、遍历网卡
    for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == NULL) {
            continue;
        }
        
        if (ifa->ifa_addr->sa_family == AF_INET) {
            struct sockaddr_in* addr = (struct sockaddr_in*)ifa->ifa_addr;
            if (strcmp(inet_ntoa(addr->sin_addr), node) == 0) {
                strncpy(interface, ifa->ifa_name, 256);
                break;
            }
        } else if (ifa->ifa_addr->sa_family == AF_INET6) {
            struct sockaddr_in6* addr = (struct sockaddr_in6*)ifa->ifa_addr;
            char addr_str[INET6_ADDRSTRLEN];
            if(inet_ntop(AF_INET6, &addr->sin6_addr, addr_str, sizeof(addr_str)) != NULL) {
                if (strcmp(addr_str, node) == 0) {
                    strncpy(interface, ifa->ifa_name, 256);
                    break;
                }
            }
        }
    }
    freeifaddrs(ifaddr);

    if (interface == NULL) {
        perror("get interface failed");
        return -errno;
    }
    return 0;
}


bool is_current_ctdb_node_ok() {
    FILE *fp = popen(CTDB_STATUS_COMMAND, "r");
    if (!fp) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() %s execute failed, %s--", __func__, CTDB_IP_COMMAND, strerror(errno));
		return false;
    }

    char line[256];
    bool is_ok = false;

    while (fgets(line, sizeof(line), fp)) {
        if (strstr(line, "(THIS NODE)")) {  // 定位当前节点
            is_ok = (strstr(line, "OK|") != NULL);  // 检查OK状态
            break;
        }
    }
    pclose(fp);
    return is_ok;
}

/*
 * @brief 获取本地虚拟IP列表
 *
 * @param num 指向虚拟IP个数的指针
 * @param virip 用于存储虚拟IP的字符数组
 * @return int 成功返回0，失败返回-1
 */
int get_local_virip(int *num, char virip[][INET6_ADDRSTRLEN])
{
	if (!is_current_ctdb_node_ok()) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() ctdb is not ok", __func__);
		return -1;
	}
	// 初始化数量
	*num = 0;
	
	// 执行 ctdb ip 命令
	FILE *fp = popen(CTDB_IP_COMMAND, "r");
	if (fp == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() %s execute failed, %s--", __func__, CTDB_IP_COMMAND, strerror(errno));
		return -1;
	}

	char line[1024] = {0};

	// 读取第一行，提取节点号
	if (fgets(line, sizeof(line), fp) == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() %s fgets failed, line: %s--", __func__, CTDB_IP_COMMAND, line);
		pclose(fp);
		return -1;
	}

	if (strstr(line, "Public IPs on node") == NULL) {
		pclose(fp);
		return -1;
	}

	int in = 0;
	char *p[20];
	char *buf = line;
	char *inner_ptr = NULL;

	// 分割第一行提取节点号
	while ((p[in] = strtok_r(buf, " ", &inner_ptr)) != NULL) {
		in++;
		buf = NULL;
	}

	int node = atoi(p[in - 1]);

	// 逐行读取后续数据
	while (!feof(fp)) {
		memset(line, 0, sizeof(line));
		if (fgets(line, sizeof(line), fp) != NULL) {
			char *tnode = NULL;
			char *tip = NULL;
			char *lasts;

			tip = strtok_r(line, " ", &lasts);
			if (tip == NULL) {
				pclose(fp);
				return -1;
			}

			tnode = strtok_r(NULL, " ", &lasts);  // 获取该行的 node 值
			if (tnode == NULL) {
				pclose(fp);
				return -1;
			}

			int node1 = atoi(tnode);

			if (node != node1)
				continue;

			// 存储匹配的虚拟IP
			if (*num < MAX_VIP) {
				strncpy(virip[*num], tip, INET_ADDRSTRLEN - 1);
				virip[*num][INET_ADDRSTRLEN - 1] = '\0';  // 确保字符串以 '\0' 结尾
				(*num)++;
			} else {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() ip_num:%d exceed MAX_VIP:%d", __func__, *num, MAX_VIP);
			}
			
		}
	}

	pclose(fp);
	return 0;
}

char *get_ip_by_fi(struct fi_info *fi) {
	if (!fi || !fi->src_addr) {
		return NULL;
	}
	static char ip_str[INET6_ADDRSTRLEN];
	struct sockaddr *sa = (struct sockaddr *)fi->src_addr;

	if (sa->sa_family == AF_INET) {
		struct sockaddr_in *sa4 = (struct sockaddr_in *)sa;
		inet_ntop(AF_INET, &sa4->sin_addr, ip_str, INET6_ADDRSTRLEN);
	} else if (sa->sa_family == AF_INET6) {
		struct sockaddr_in6 *sa6 = (struct sockaddr_in6 *)sa;
		inet_ntop(AF_INET6, &sa6->sin6_addr, ip_str, INET6_ADDRSTRLEN);
	} else {
		return NULL;
	}
	return ip_str;
}


struct fi_info *get_fi_by_ip(const char *ip, const char *port, struct fi_info *hints) {
	if (!*ip || !ip || !port || !hints) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() Invalid argument", __func__);
		return NULL;
	}

	//生成临时hints
	struct fi_info *temp_hints = fi_dupinfo(hints);
	if (!temp_hints) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() fi_dupinfo failed", __func__);
		return NULL;
	}

	struct fi_info *fi = NULL;
	struct addrinfo *res = NULL;

	//getaddrinfo
	int ret = getaddrinfo(ip, port, NULL, &res);
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() getaddrinfo failed", __func__);
		fi_freeinfo(temp_hints);
		return NULL;
	}

	temp_hints->src_addr = (void*)res->ai_addr;
	temp_hints->src_addrlen = res->ai_addrlen;
	
	//fi_getinfo
	ret = fi_getinfo(FIVER, ip, port, 0, temp_hints, &fi);
	if (ret) {
		MSG_PRINTERR("fi_getinfo", ret);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() NFS/FABRIC fi_getinfo error, ret=%d, ip=%s", __func__, ret, ip);
		fi = NULL;
	}

	//释放hints
	if (temp_hints) {
		temp_hints->src_addr = NULL;
	        temp_hints->src_addrlen = 0;
		fi_freeinfo(temp_hints);
	}
	if (res) freeaddrinfo(res);
	return fi;
	
}

//1、ip->net_dev->domain
struct fabric_domain *get_domain_by_ip(fabric_class *fab_clas, const char *ip) {
	if (!fab_clas || !ip || !*ip) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() Invalid argument", __func__);
		return NULL;
	}

	//通过IP，获取网卡名
	char netdev[DEVSIZE] = {0};
	int ret = get_netdev_by_addr(ip, netdev);
	if (ret != 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() failed to get netdev for IP %s, ret=%s",  __func__, ip, strerror(ret));
		return NULL;
	}

	//查找对应网卡的domain
	pthread_mutex_lock(&fab_clas->domain_lock);
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if (fab_clas->domains[i] && strcmp(fab_clas->domains[i]->netdev, netdev) == 0) {
			//find it
			pthread_mutex_unlock(&fab_clas->domain_lock);
			return fab_clas->domains[i];
		}
	}
	pthread_mutex_unlock(&fab_clas->domain_lock);

	//未找到
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() connot get domain for netdev:%s, IP:%s, we need creat it",  __func__, netdev, ip);
	return NULL;
	
}

struct fabric_domain *get_domain_by_fi(fabric_class *fab_clas, struct fi_info *fi) {
	if (!fi || !fab_clas) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() Invalid argument", __func__);
		return NULL;
	}

	char *ip = get_ip_by_fi(fi);
	if (!ip) return NULL;

	return get_domain_by_ip(fab_clas, ip);
}

int fabric_creat_domain_and_start_listen(fabric_class *fab_clas, const char *ip, RDMAXPRT *xd) {
	if (!fab_clas || !ip) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() fab_clas or ip is NULL", __func__);
		return -1;
	}

	char port[20] = {0};
	snprintf(port, sizeof(port), "%d", fab_clas->attr->nfsrdma_port);

	int ret;

	struct fi_info *fi;
	//1、创建fi
	fi = get_fi_by_ip(ip, port, fab_clas->conf->hints);
	if (!fi) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() get_fi_by_ip failed IP %s",  __func__, ip);
		return -1;
	}
	//fi_fabric and eq_open; 只做一次就可以了
	if (!fab_clas->effect_fi) {
		fab_clas->fi = fi;
		fab_clas->effect_fi = fab_clas->fi;
		ret = fi_fabric(fab_clas->effect_fi->fabric_attr, &fab_clas->fabric, NULL);
		if (ret) {
			MSG_PRINTERR("fi_fabric", ret);
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"%s() NFS/FABRIC fi_fabric error, ret %d", __func__, ret);
			goto err_fi;
		}

		ret = fi_eq_open(fab_clas->fabric, &fab_clas->conf->eq_attr, &fab_clas->eq, NULL);
		if (ret) {
			MSG_PRINTERR("fi_eq_open", ret);
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"%s() NFS/FABRIC fi_eq_open error, ret %d", __func__, ret);
			goto err_fi;
		}
	}

	struct fabric_domain *domain;
	//2、判断这个IP所在网卡是否已经创建过domain
	domain = get_domain_by_ip(fab_clas, ip);
	
	pthread_mutex_lock(&fab_clas->domain_lock);
	if (domain != NULL) {
		//已经有所属domain，直接添加IP进去
		ret = add_ip_to_domain(ip, domain);
		if (ret != 0) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() failed to add vip:%s to domain:%p", __func__, ip, domain);
			pthread_mutex_unlock(&fab_clas->domain_lock);
			goto err_fi;
		}
	} else {
		//创建domain
		domain = fabric_open_domain(fab_clas, fi, xd);
		if (domain == NULL) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() fabric_open_domain failed!", __func__);
			pthread_mutex_unlock(&fab_clas->domain_lock);
			goto err_fi;
		}
		//添加IP到domain
		ret = add_ip_to_domain(ip, domain);
		if (ret != 0) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() failed to add vip:%s to domain:%p", __func__, ip, domain);
			pthread_mutex_unlock(&fab_clas->domain_lock);
			goto err_domain;
		}

		fab_clas->domains[domain->index] = domain;
		fab_clas->domain_count++;
	}
	pthread_mutex_unlock(&fab_clas->domain_lock);
	
	//3、创建并监听PEP: fi_passive_ep -> fi_pep_bind -> listen
	ret = fabric_open_pep_listen(fab_clas,fi, ip, domain);
	if (ret != 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() failed to fabric_open_pep_listen vip:%s to domain:%p", __func__, ip, domain);
		goto err_domain;
	}
	
	__warnx(TIRPC_DEBUG_FLAG_ERROR,"%s() successfuly add ip:%s to domain:%p", __func__, ip, domain);
	fi_freeinfo(fi);
	return 0;
	
err_domain:
	if (domain->tx_cq)
		free(domain->tx_cq);
	if (domain->rx_cq)
		free(domain->rx_cq);
	if (domain)
		free(domain);
	
err_fi:
	if (fi)
		fi_freeinfo(fi);
	return -1;
}

int add_ip_to_domain(const char *ip, struct fabric_domain *domain) {
	if (!ip || !domain) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() Invalid argument", __func__);
		return -1;
	}

	char netdev[DEVSIZE] = {0};
	int ret = get_netdev_by_addr(ip, netdev);
	if (ret != 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,"%s() get_netdev_by_addr failed for IP %s, ret=%d", __func__, ip, ret);
		return -1;
	}
	
	//将网卡名捆绑到domain
	strncpy(domain->netdev, netdev, DEVSIZE - 1);
	domain->netdev[DEVSIZE - 1] = '\0';

	//添加该虚拟IP到domain的ip list中(每次都加到最后一个：ip_count)
	strncpy(domain->ip_list[domain->ip_count], ip, INET6_ADDRSTRLEN - 1);
	domain->ip_list[domain->ip_count][INET6_ADDRSTRLEN - 1] = '\0';
	domain->ip_count++;
	
	return 0;
}

int delete_ip_from_domain(fabric_class *fab_clas, const char *vip, struct fabric_domain *domain) {
	if (!vip || !domain) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() Invalid argument", __func__);
		return -1;
	}

	pthread_mutex_lock(&fab_clas->domain_lock);
	//从该domain中找到该IP位置
	int ip_idx = -1;
	int i;
	for (i = 0; i < domain->ip_count; i++) {
		if (strcmp(domain->ip_list[i], vip) == 0) {
			ip_idx = i;
			break;
		}
	}
	
	//如果找到了该IP，将其从列表中移除
	if (ip_idx >= 0) {
		for (i = ip_idx; i < domain->ip_count - 1; i++) {
			strncpy(domain->ip_list[i], domain->ip_list[i+1], INET6_ADDRSTRLEN - 1);
			domain->ip_list[i][INET6_ADDRSTRLEN - 1] = '\0';
		}
		domain->ip_list[domain->ip_count - 1][0] = '\0';
		domain->ip_count--;
	} else {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() connot find vip:%s in domain:%p, ip_count:%d", __func__, vip, domain, domain->ip_count);
		for (i = 0; i < domain->ip_count; i++) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() ip_index:%d, ip:%s ", __func__, i, domain->ip_list[i]);
		}
		pthread_mutex_unlock(&fab_clas->domain_lock);
		return -2;
	}
	pthread_mutex_unlock(&fab_clas->domain_lock);
	return 0;
}

struct fabric_domain *fabric_open_domain(fabric_class *fab_clas, struct fi_info *fi_for_domain, RDMAXPRT *xd)
{
	int ret = FI_SUCCESS;
	int i = 0;
	
	if (!fab_clas || !fi_for_domain) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() invalid parameters",  __func__);
		return NULL;
	}

	int domain_index = -1;
	for (i = 0; i < MAX_DEV; i++) {
		if (fab_clas->domains[i] == NULL) {
			domain_index = i;
			break;
		}
	}
	
	if (domain_index == -1) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() no available domain slot",  __func__);
	}

	//分配domain内存
	struct fabric_domain *domain = (struct fabric_domain *)calloc(1, sizeof(struct fabric_domain));
	if (domain == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() calloc domain failed", __func__);
		return NULL;
	}
	
	domain->fab_clas = fab_clas;
	domain->index = domain_index;
	ret = fi_domain(fab_clas->fabric, fi_for_domain, &domain->fi_domain, NULL);
	if (ret) {
		MSG_PRINTERR("fi_domain open", ret);
		goto error;
	}
	
	domain->tx_cq = (struct fid_cq **)calloc(fab_clas->attr->thread_cq_num, sizeof(struct fid_cq));
	if (domain->tx_cq == NULL) {
		MSG_PRINTERR("fi_domain txcq create error", ret);
		goto error;
	}
	
	domain->rx_cq = (struct fid_cq **)calloc(fab_clas->attr->thread_cq_num, sizeof(struct fid_cq));
	if (domain->rx_cq == NULL) {
		free(domain->tx_cq);
		MSG_PRINTERR("fi_domain rxcq create error", ret);
		goto error;
	}

	for (i=0; i<fab_clas->attr->thread_cq_num; i++) {
		ret = fi_cq_open(domain->fi_domain, &fab_clas->conf->cq_attr, (struct fid_cq **)&domain->tx_cq[i], &domain->tx_cq[i]);
		if (ret) {
			MSG_PRINTERR("fi_cq_open tx", ret);
			goto error;
		}

		ret = fi_cq_open(domain->fi_domain, &fab_clas->conf->cq_attr, (struct fid_cq **)&domain->rx_cq[i], &domain->rx_cq[i]);
		if (ret) {
			MSG_PRINTERR("fi_cq_open rx", ret);
			goto error;
		}
	}

	pthread_mutex_init(&(domain->map_lock), NULL);
	domain->ep_map = NULL;
	domain->ep_destory_map = NULL;
	domain->ip_count = 0;
	
	if (domain_index == 0) {
		//申请内存
		domain->mpool = mpool_init(&fab_clas->conf->pool_attr);
		xd->buffer_aligned = alloc_registed_memory(xd->sm_dr.pagesz, xd->buffer_total);	//只申请内存，注册放外面
		assert(xd->buffer_aligned != NULL);
	} else {
		domain->mpool = fab_clas->domains[0]->mpool;
	}

	//注册pool_mr内存
	ret = fi_mr_reg(domain->fi_domain, domain->mpool->p, domain->mpool->total_size,
			FI_REMOTE_READ | FI_REMOTE_WRITE | FI_WRITE | FI_READ,
			0, 0, 0, &domain->pool_mr, NULL);
	if (ret) {
		MSG_PRINTERR("fi_mr_reg", ret);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() NFS/FABRIC fi_mr_reg pool_mr error, domain_index:%d, ret %d", __func__, domain_index, ret);
		goto error;
	}

	//注册gane_mr内存
	char *buf = NULL;
	uint64_t size;
	buf = (char *)xd->buffer_aligned;
	size = xd->buffer_total;
	ret = fi_mr_reg(domain->fi_domain, buf, size,
			FI_REMOTE_READ | FI_REMOTE_WRITE | FI_WRITE | FI_READ,
			0, 0, 0, &domain->gane_mr, NULL);
	if (ret) {
		MSG_PRINTERR("fi_mr_reg", ret);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() NFS/FABRIC fi_mr_reg gane_mr error, domain_index:%d, ret %d", __func__, domain_index, ret);
		goto error;
	}
	return domain;

error:
  	if (domain->tx_cq)
		free(domain->tx_cq);
	if (domain->rx_cq)
		free(domain->rx_cq);
	if (domain)
		free(domain);
	return NULL;
}

void destory_map_process(fabric_class *fab_clas, struct fabric_domain *domain, int thread_id) {
	if (domain->ep_destory_map != NULL && thread_id == 0 ) {
		int svc_connections = 0;
		fabric_endpoint *fab_ep = NULL;
		fabric_endpoint *fab_ep_tmp = NULL;
		pthread_mutex_lock(&(domain->map_lock));
		HASH_ITER(hh, domain->ep_destory_map, fab_ep, fab_ep_tmp) {
			if (atomic_fetch_uint64_t(&fab_ep->ref_count) == 0) {
				int svc_connections = 0;
				HASH_DEL(domain->ep_destory_map, fab_ep);
				wait_destory_ep_num --;
				probe_count_dec(PROBE_COUNT_WAITDESTORY);
				svc_rdma_fabric_xprt_destory(fab_ep->xd);
				svc_connections = svc_vc_dec_nconns();
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC destory fab_ep %p, total eps count %d, wait_destory_ep=%d, svc_connections %d.", 
					__func__, fab_ep, cur_eps_count, wait_destory_ep_num, svc_connections);
			} else {
				if (fab_ep->active == false){
					if (svc_vc_new_conn_ok() == true) {
						svc_connections = svc_get_connections();
						fab_ep->active = true;
						__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric %p connect	active, cur connections(allxprt) %d,", 
								__func__, fab_ep, svc_connections);
					}
				}
			}
		}
		pthread_mutex_unlock(&(domain->map_lock));
	}
}
/*
static int get_ibdev_numa_node(const char* ibdev)
{
    //获取设备numa node
    FILE *fp;
    char device_path[256];
    snprintf(device_path, sizeof(device_path), "/sys/class/infiniband/%s/device/numa_node", ibdev);
    fp = fopen(device_path, "r");
    if (fp == NULL) {
        perror("fopen");
	    return -errno;
    }

    fscanf(fp, "%d", &numa_node);
    fclose(fp);

    return 0;
}
*/
#if 0
/**
 * device name, cpu0 affinity, cpu1 affinity
 * */
char *cpu_affinity_config[10][3] = {
	{"AS1300G6-CG36", "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15", ""},
	{"AS1300G6-CG35", "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15", ""},
};

#endif
static int init_cpu_affinity(const struct rpc_rdma_attr *attr)
{
	//tx cpu affinity
	char *token = NULL;
	char* cpu_affinity;
	int i = 0;
	int j = 0;

	for (j=0; j<CPU_NUM; j++)
		thread_cpus[j] = -1;
	if (numa_node >= 0 && enable_cpu_affinity) {
		//默认
		//0，1，2，3 cpu 0
		//4，5，6，7 cpu 1
		if (numa_node <= 3) {
			cq_cpu = attr->cq_cpu_affinity_0;
			rdma_mem_affinity = attr->rdma_mem_affinity_0;
			cpu_affinity = attr->thread_cpu_affinity_0;
		} else {
			cq_cpu = attr->cq_cpu_affinity_1;
			rdma_mem_affinity = attr->rdma_mem_affinity_1;
			cpu_affinity = attr->thread_cpu_affinity_1;
		}

		//worker cpu affinity
		token = strtok(cpu_affinity, ",");
		while (token != NULL) {
			if (sscanf(token, "%d", &thread_cpus[i]) == 1) {
				i++;
			}
			token = strtok(NULL, ",");
		}
	}

    return 0;
}

#if 0
static int init_cpu_affinity(const struct rpc_rdma_attr *attr)
{
    //tx cpu affinity
    char *token = NULL;
    char* worker_cpu_affinity;

    if (numa_node >= 0) {
        //默认
        //0，1，2，3 cpu 0
        //4，5，6，7 cpu 1
        if (numa_node <= 3) {
            cq_cpu = attr->cq_cpu_affinity_0;
            rdma_mem_affinity = attr->rdma_mem_affinity_0;
            worker_cpu_affinity = attr->worker_cpu_affinity_0;
        } else {
            cq_cpu = attr->cq_cpu_affinity_1;
            rdma_mem_affinity = attr->rdma_mem_affinity_1;
            worker_cpu_affinity = attr->worker_cpu_affinity_1;
        }

        //worker cpu affinity
        token = strtok(worker_cpu_affinity, ",");
	while (token != NULL) {
	    if (sscanf(token, "%d", &worker_cpus[worker_cpu_num]) == 1) {
	        worker_cpu_num ++;
	    }
	    token = strtok(NULL, ",");
	}

        if (worker_cpu_num > 0) {
            binding = true;
        }

    }

    return 0;
}
#endif
char *alloc_registed_memory(long page_size, uint64_t size)
{
	int ret;
	char *buf;
	buf = mem_aligned(page_size, size);
	//buf = (char *)mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_PRIVATE | MAP_ANONYMOUS | MAP_POPULATE, -1, 0);
	//内存绑定numa
	if (rdma_mem_affinity >= 0) {
		unsigned long nodemask;
		nodemask = 1UL << rdma_mem_affinity;
		ret = mbind(buf, size, MPOL_BIND, &nodemask, sizeof(nodemask), MPOL_MF_MOVE);
		assert(ret == 0);
	}
	
	/*pthread_mutex_lock(&fab_clas->domain_lock);
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if (fab_clas->domains[i] && fab_clas->domains[i]->fi_domain) {
			ret = fi_mr_reg(fab_clas->domains[i]->fi_domain, buf, size,
					FI_REMOTE_READ | FI_REMOTE_WRITE | FI_WRITE | FI_READ,
					0, 0, 0, &fab_clas->domains[i]->gane_mr, NULL);
			if (ret) {
				MSG_PRINTERR("fi_mr_reg", ret);
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() failed to register memory for domain %d",  __func__, i);
				free(buf);
				return NULL;
			}
		}
	}
	pthread_mutex_unlock(&fab_clas->domain_lock);*/

	return buf;
}

extern int total_thread_cnt;
fabric_class *fabric_class_alloc(struct rpc_rdma_attr *attr)
{
	struct fabric_class *fab_clas = NULL;
	//int i = 0;
	fab_clas = (struct fabric_class *)malloc(sizeof(struct fabric_class));
	if (fab_clas == NULL) {
		return NULL;
	}
	memset(fab_clas, 0, sizeof(struct fabric_class));
	fab_clas->conf = init_config(attr);
	fab_clas->fi = NULL;
	fab_clas->fabric = NULL;
	
	/* 初始化domain数组 */
	fab_clas->domain_count = 0;
	memset(fab_clas->domains, 0, sizeof(fab_clas->domains));
	
	fab_clas->attr = attr;
	fab_clas->eps_list = (struct fabric_eps_list *)malloc(sizeof(struct fabric_eps_list)* attr->thread_cq_num);
	fab_clas->ifaddr_list = NULL;
	fab_clas->fab_pep = NULL;
	if (fab_clas->eps_list == NULL) {
		free(fab_clas->conf);
		free(fab_clas);
		return NULL;
	}
	memset(fab_clas->eps_list, 0, sizeof(struct fabric_eps_list)* attr->thread_cq_num);
	//for (i=0; i<attr->thread_cq_num; i++) {
	//	pthread_mutex_init(&fab_clas->eps_list[i].map_lock, NULL);
	//}

	// 线程池创建移到fabric_init中所有domain创建成功之后
	// 这里只初始化基本结构，不创建线程池

	pthread_mutex_init(&fab_clas->pep_lock, NULL);
	pthread_mutex_init(&fab_clas->domain_lock, NULL);
	return fab_clas;
}

int fabric_free(fabric_class* fab_clas)
{
	/* 释放所有domain */
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if (fab_clas->domains[i] != NULL) {
			destory_domain(fab_clas->domains[i]);
		}
	}

	if (fab_clas->attr->thr_nolock == true) {
		fab_clas->stop = 1;
		thpool_wait(fab_clas->cq_thpool);
		thpool_destroy(fab_clas->cq_thpool);
		thpool_wait(fab_clas->work_thpool);
		thpool_destroy(fab_clas->work_thpool);
		thpool_wait(fab_clas->rpc_thpool);
		thpool_destroy(fab_clas->rpc_thpool);
		thpool_wait(fab_clas->qos_thpool);
		thpool_destroy(fab_clas->qos_thpool);
	}

	fi_freeinfo(fab_clas->conf->hints);
	free(fab_clas->conf);
	fi_freeinfo(fab_clas->fi);

	/* 释放所有pep */
	struct fabric_pep *ppep = fab_clas->fab_pep;
	while (ppep) {
		struct fabric_pep *next = ppep->next;
		if (ppep->pep) {
			fi_close(&ppep->pep->fid);
		}
		ppep->domain = NULL;
		free(ppep);
		ppep = next;
	}
	if (fab_clas->fabric)
		fi_close(&fab_clas->fabric->fid);
	pthread_mutex_destroy(&fab_clas->pep_lock);
	pthread_mutex_destroy(&fab_clas->domain_lock);
	free(fab_clas);
	return 0;
}

bool get_listen_net_devicename(void *device_name, int size)
{
	FILE *fp;
	char buffer[256] = {0};
	char ib_dev[256]  = {0};
	fp = popen("cat /etc/ctdb/public_addresses  |  awk 'NR==1 {print $2}'", "r");
	if (fp == NULL){
		__warnx(TIRPC_DEBUG_FLAG_ERROR, 
			"%s() NFS/FABRIC popen error ", __func__);
		return false;
	}else{
		while (fgets(buffer, sizeof(buffer), fp) !=NULL){
			size_t len = strcspn(buffer, "\n");
			buffer[len]='\0';			
			netdev_to_ibdev(buffer, ib_dev);
			__warnx(TIRPC_DEBUG_FLAG_ERROR, 
				"%s() NFS/FABRIC popen get listen device %s ,ibdev %s", __func__, buffer, ib_dev);
			printf("%s() popen get device %s ,ibdev %s\n", __func__, buffer, ib_dev);
		}
		strncpy(device_name, ib_dev, size);
		pclose(fp);
		return true;
	}
}

fabric_class* fabric_init(char *node, int service, const struct rpc_rdma_attr *attr, RDMAXPRT *xd)
{
	int ret;
	struct rpc_rdma_attr *xa = (struct rpc_rdma_attr *)attr;

	char vip_list[MAX_VIP][INET6_ADDRSTRLEN];
	int addrsize = 0;
	ret = get_local_virip(&addrsize, vip_list);
	if (ret != 0 || addrsize <= 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() no vir ip", __func__);
		return NULL;
	}
	
	wait_destory_ep_num_max = xa->wait_destory_ep_num_max;
	wait_destory_ep_num_max_abort = xa->wait_destory_ep_num_max_abort;
	max_eps_connections = xa->max_connections;
	enable_debug_cq = xa->enable_debug_cq;
	enable_cpu_affinity = xa->enable_cpu_affinity;
	/* 双网卡监听，numa暂时无方案
	char ibdev[256] = {0};
	get_listen_net_devicename(ibdev, sizeof(ibdev));
	if (strlen(ibdev) == 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() NFS/FABRIC get_listen_net_devicename error, ibdev %s", __func__, ibdev);
		//goto error;
	}

#if _NUM_AFFINITY
	char interface[256] = {0};
	get_netdev_by_addr(node, interface);
	netdev_to_ibdev(interface, ibdev);
	//get_ibdev_numa_node(ibdev);
#endif
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC get_ibdev_numa_node for %s", __func__, ibdev);
	get_ibdev_numa_node(ibdev);*/
	init_cpu_affinity(xa);

	fab_clas = fabric_class_alloc(xa);
	fab_clas->thr_nolock = attr->thr_nolock;
	fab_clas->thr_work_send = attr->enable_workth_send;

	int i;
	int try_count;
	for (i =0 ; i < addrsize; i++) {
		ret = fabric_creat_domain_and_start_listen(fab_clas, vip_list[i], xd);
		try_count = 1;
		while (ret != 0 && try_count <= 2) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  fabric_creat_domain_and_start_listen failed, vip_list=%s, ret=%d, try again", __func__, vip_list[i], ret);
			ret = fabric_creat_domain_and_start_listen(fab_clas, vip_list[i], xd);
			try_count++;
			sleep(1);
		} 
		if (ret != 0) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  fabric_creat_domain_and_start_listen failed, vip_list=%s, ret=%d", __func__, vip_list[i], ret);
			goto error;
		}
	}

	// 所有domain和listen都成功后，再创建和启动线程池
	if (xa->thr_nolock == true){
		if (xa->enable_rpc_thread == true) {
			total_thread_cnt = xa->thread_cq_num +
				xa->thread_work_num + xa->thread_rpc_num + xa->thread_qos_num;
			fab_clas->cq_thpool = thpool_init_nolock("cq_thpool", xa->thread_cq_num);
			fab_clas->rpc_thpool = thpool_init_nolock("rpc_thpool", xa->thread_rpc_num);
			fab_clas->work_thpool = thpool_init_nolock("work_thpool", xa->thread_work_num);
			fab_clas->qos_thpool = thpool_init_nolock("qos_thpool", xa->thread_qos_num);
		} else {
			total_thread_cnt = xa->thread_cq_num + xa->thread_work_num;
			fab_clas->cq_thpool = thpool_init_nolock("cq_thpool", xa->thread_cq_num);
			fab_clas->work_thpool = thpool_init_nolock("work_thpool", xa->thread_work_num);
		}

		// 检查线程池创建是否成功
		if (!fab_clas->cq_thpool || !fab_clas->work_thpool ||
		    (xa->enable_rpc_thread && (!fab_clas->rpc_thpool || !fab_clas->qos_thpool))) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC thread pool creation failed", __func__);
			goto error;
		}

		//fabric_cqpool_submit(fab_clas, cq_thread_callback, fab_clas);
		//thpool_add_job_nolock(fab_clas->cq_thpool, cq_thread_callback, fab_clas);
		int i = 0;
		for (; i<fab_clas->attr->thread_cq_num; i++) {
			thpool_add_job_nolock_withthrid(fab_clas->cq_thpool, cq_thread_callback, fab_clas, i);
		}
		thpool_* thpool_p = fab_clas->qos_thpool;
		for (i = 0; i<fab_clas->attr->thread_qos_num; i++) {
			thpool_add_job_nolock_withthrid(fab_clas->qos_thpool, qos_thread_do, thpool_p, i + thpool_p->self_thr_index_min);
		}
	} else {
		fab_clas->rx_thpool = thpool_init("rx_thpool", xa->thread_rx_num);
		fab_clas->tx_thpool = thpool_init("tx_thpool", xa->thread_tx_num);

		if (!fab_clas->rx_thpool || !fab_clas->tx_thpool) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC thread pool creation failed", __func__);
			goto error;
		}
	}

	return fab_clas;

error:
	if (fab_clas) {
		// 如果线程池已经创建，需要先清理
		if (fab_clas->attr && fab_clas->attr->thr_nolock == true) {
			if (fab_clas->cq_thpool) {
				fab_clas->stop = 1;
				thpool_wait(fab_clas->cq_thpool);
				thpool_destroy(fab_clas->cq_thpool);
			}
			if (fab_clas->work_thpool) {
				thpool_wait(fab_clas->work_thpool);
				thpool_destroy(fab_clas->work_thpool);
			}
			if (fab_clas->rpc_thpool) {
				thpool_wait(fab_clas->rpc_thpool);
				thpool_destroy(fab_clas->rpc_thpool);
			}
			if (fab_clas->qos_thpool) {
				thpool_wait(fab_clas->qos_thpool);
				thpool_destroy(fab_clas->qos_thpool);
			}
		} else {
			if (fab_clas->rx_thpool) {
				thpool_wait(fab_clas->rx_thpool);
				thpool_destroy(fab_clas->rx_thpool);
			}
			if (fab_clas->tx_thpool) {
				thpool_wait(fab_clas->tx_thpool);
				thpool_destroy(fab_clas->tx_thpool);
			}
		}

		// 清理其他资源
		if (fab_clas->conf) {
			free(fab_clas->conf);
		}
		if (fab_clas->eps_list) {
			free(fab_clas->eps_list);
		}

		// 销毁互斥锁（如果已初始化）
		pthread_mutex_destroy(&fab_clas->pep_lock);
		pthread_mutex_destroy(&fab_clas->domain_lock);

		free(fab_clas);
		fab_clas = NULL;
	}
	return NULL;
}

fabric_endpoint *fabric_open_ep(fabric_class *fab_clas, struct fi_info *info)
{
	int ret = FI_SUCCESS;
	fabric_endpoint *fab_endp = (fabric_endpoint*)malloc(sizeof(fabric_endpoint));
	if (fab_endp == NULL) {
		ret = -errno;
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_open_ep malloc failed", __func__);
		MSG_PRINTERR("malloc failed", ret);
		return NULL;
	}
	memset(fab_endp, 0, sizeof(fabric_endpoint));
	fab_endp->fi = info;
	fab_endp->state = EP_STATE_INIT;

	struct fabric_domain *domain;
	domain = get_domain_by_fi(fab_clas, info);
	if (!domain) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_open_ep invalid info %p", __func__, info);
		goto error;
	}
	
	//使用找到的domain
	fab_endp->domain = domain;
	fab_endp->mpool = fab_endp->domain->mpool;
	fab_endp->pool_mr = fab_endp->domain->pool_mr;
	fab_endp->gane_mpool = fab_endp->domain->gane_mpool;
	fab_endp->gane_mr = fab_endp->domain->gane_mr;
	
	memset(&fab_endp->hh, 0, sizeof(fab_endp->hh));
	assert(fab_endp->gane_mr != NULL);

	//创建endpoint
	ret = fi_endpoint(fab_endp->domain->fi_domain, fab_endp->fi, &fab_endp->ep, fab_endp);
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_open_ep fi_endpoint %d for domain %p ", __func__, ret, domain);
		goto error;
	}
	
	fabric_add_ep_list(fab_clas, fab_endp);
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC fab_ep %p. dispense thrdid %d. ", __func__, fab_endp, fab_endp->thrid);
	ret = fabric_enable_ep(fab_clas, fab_endp);
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_open_ep fabric_enable_ep %d", __func__, ret);
		MSG_PRINTERR("fabric_enable_ep", ret);
		goto error;
	}

	// 初始化接收队列
	ret = fabric_ep_prepare_recv(fab_endp);
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_open_ep fabric_ep_prepare_recv %d", __func__, ret);
		MSG_PRINTERR("fabric_ep_prepare_recv", ret);
		goto error;
	}

	return fab_endp;

  error:
	free(fab_endp);
	return NULL;
}

int fabric_enable_ep(fabric_class *fabric_clas, fabric_endpoint *fabric_ep)
{
	//bind eq,cq
	int ret;
	uint64_t flags;
	int thr_index = fabric_ep->thrid;

	ret = fi_ep_bind(fabric_ep->ep, &fabric_clas->eq->fid, 0);
	if (ret) {
		MSG_PRINTERR("fi_ep_bind", ret);
		return ret;
	}

	flags = FI_TRANSMIT;/*tx_cq_bind_flags*/
	ret = fi_ep_bind(fabric_ep->ep, &fabric_ep->domain->tx_cq[thr_index]->fid, flags);
	if (ret) {
		MSG_PRINTERR("fi_ep_bind", ret);
		return ret;
	}

	flags = FI_RECV;/*tx_cq_bind_flags*/
	ret = fi_ep_bind(fabric_ep->ep, &fabric_ep->domain->rx_cq[thr_index]->fid, flags);
	if (ret) {
		MSG_PRINTERR("fi_ep_bind", ret);
		return ret;
	}

	ret = fi_enable(fabric_ep->ep);
	if (ret) {
		MSG_PRINTERR("fi_enable", ret);
		return ret;
	}

	return FI_SUCCESS;
}


/* 根据init过程种创建的fabric_clas->fi链表，挨个创建PEP */
int fabric_open_pep_listen(fabric_class *fab_clas, struct fi_info *fi, const char *ip, struct fabric_domain *domain)
{
	int ret = FI_SUCCESS;

	/* 创建并监听PEP: fi_passive_ep -> fi_pep_bind -> */
	struct fabric_pep *newppep = (struct fabric_pep*)malloc(sizeof(struct fabric_pep));
	if (!newppep) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC malloc newpep failed", __func__);
		ret = -ENOMEM;
		return ret;
	}
	memset(newppep, 0, sizeof(struct fabric_pep));
	memset(newppep->ip, 0, INET6_ADDRSTRLEN);
	
	ret = fi_passive_ep(fab_clas->fabric, fi, &newppep->pep, domain);
	
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,"fi_passive_ep failed");
		goto err_p;
	}

	ret = fi_pep_bind(newppep->pep, &fab_clas->eq->fid, 0);
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,"fi_pep_bind failed");
		goto err_p;
	}

	ret = fi_listen(newppep->pep);
	if (ret) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,"fi_listen %s failed", ip);
		goto err_p;
	}

	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC start linsten vip=%s.", __func__, ip);

	//记录IP到fabric_pep
	strncpy(newppep->ip, ip, INET6_ADDRSTRLEN - 1);
	newppep->ip[INET6_ADDRSTRLEN - 1] = '\0';
	//记录domain到fabric_pep
	newppep->domain = domain;

	pthread_mutex_lock(&fab_clas->pep_lock);
	/* 挂到fab_clas->fab_pep链表头 */
	newppep->next = fab_clas->fab_pep;
	fab_clas->fab_pep = newppep;
	pthread_mutex_unlock(&fab_clas->pep_lock);
	
	return ret;
	
err_p:
	if (newppep) {
		if (newppep->pep)
			fi_close(&newppep->pep->fid);
		free(newppep);
	}
	return ret;
}

int fabric_listen(struct fabric_pep *fab_pep)
{
	int ret = FI_SUCCESS;

	struct fabric_pep *fpep = NULL;
	for (fpep = fab_pep; fpep != NULL && fpep->next != NULL && fpep->pep != NULL; fpep = fpep->next){
		__warnx(TIRPC_DEBUG_FLAG_ERROR,"fabric_listen p_ep->pep %p", fpep->pep);
		ret = fi_listen(fpep->pep);
		if (ret) {
			MSG_PRINTERR("fabric_listen", ret);
		}
	}
	return ret;
}


int fabric_recv(fabric_endpoint *fab_endp, int num)
{
	int i;
	int ret = FI_SUCCESS;
	for (i = 0; i < num; ++i) {
		probe_count_inc(PROBE_COUNT_FAB_CH);
		struct iovec *iov = (struct iovec*)malloc(1 * sizeof(struct iovec));
		probe_count_inc(PROBE_COUNT_FAB_IOV);
		chunk *ck = get_chunk(fab_endp->mpool, fab_endp->mpool->attr->chunk_size);
		if ((ck == NULL) || (ck->buffer == NULL)) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"%s() NFS/FABRIC fab_endp %p, get_chunk faild, ep->ref:%ld, wantnum:%d.", __func__, fab_endp, fab_endp->ref_count, num);
			return -1;		
		}

		iov->iov_base = (void*)ck;
		iov->iov_len = fab_endp->mpool->attr->chunk_size;

		struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
		probe_count_inc(PROBE_COUNT_FAB_CTX);
		ctx->ep = fab_endp;
		ctx->iov = iov;
		ctx->cnt = 1;
		ctx->cb_arg = NULL;
		ctx->cb = &rpc_rdma_fabric_recv_callback;
		ctx->return_code = CALLBACK_SUCEESS;

		ret = fi_recv(fab_endp->ep, ((chunk*)iov->iov_base)->buffer, fab_endp->mpool->attr->chunk_size, fab_endp->pool_mr->mem_desc, 0, ctx);
		if (ret) {
			free_chunk(fab_endp->mpool, iov->iov_base);
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"%s() NFS/FABRIC fab_endp %p, fi_recv error:%d.", __func__, fab_endp, ret);
			return ret;
		}else {
			fab_endp->fi_recv_count ++;
			(void)atomic_inc_uint64_t(&fab_endp->ref_count);	
			(void)atomic_inc_uint64_t(&fab_endp->credits);	
		}
	}

	return ret;
}

int fabric_ep_prepare_recv(fabric_endpoint *fab_endp)
{
	int ret = FI_SUCCESS;
	//申请内存，放到接收队列
	ret = fabric_recv(fab_endp, fab_endp->fi->rx_attr->size);
	if (ret) {
		MSG_PRINTERR("fabric_recv", ret);
	}else {
		//(void)atomic_add_uint64_t(&fab_endp->ref_count, fab_endp->fi->rx_attr->size);
		__warnx_rdma(TIRPC_DEBUG_FLAG_XDR_RDMA, "func %s() NFS/FABRIC fabric %p, recv ref_count %d.\n", __func__, fab_endp, fab_endp->ref_count);
	}
	return ret;
}

int destory_all_endpoint(struct fabric_domain *domain)
{
	int ret = FI_SUCCESS;
	fabric_endpoint *ep_map = domain->ep_map;

	struct fabric_endpoint *fab_ep = NULL; 
	struct fabric_endpoint *tmp_ep = NULL; 

	pthread_mutex_lock(&(domain->map_lock));
	HASH_ITER(hh, ep_map, fab_ep, tmp_ep) {
		fab_ep->state = EP_STATE_CLOSED;
		fi_close(&fab_ep->ep->fid);
		HASH_DEL(domain->ep_map, fab_ep);
		HASH_ADD(hh, domain->ep_destory_map, ep->fid, sizeof(fab_ep->ep->fid), fab_ep);
	}
	pthread_mutex_unlock(&(domain->map_lock));
	return ret;
}

int fabric_ep_release_chunk(struct fabric_endpoint *ep, struct iovec *iov, int iov_cnt)
{
	int i;
	int ret = FI_SUCCESS;

	for (i = 0; i < iov_cnt; ++i) {
		free_chunk(ep->mpool, iov[i].iov_base);
	}

	return ret;
}

void handle_sendv(void *context)
{
	int ret = 0;
	int cnt = 0;
	struct general_context *ctx = (struct general_context *)context;
	probe_timespec_end(ctx->ep->xd, &(ctx->start_sendv_handle_cb_time), RPC_fabric_send_handle_cb);
	probe_timespec_begin(ctx->ep->xd, &(ctx->start_sendv_cq_time), RPC_fabric_s_cq);
	//probe_timespec_begin(ctx->ep->xd, &(ctx->start_sendv_handle_time), RPC_fabric_send_handle);
	if (ctx->ep->state != EP_STATE_CONNECTED) {
		MSG_PRINTERR("connect state error",0);
		ctx->iov = NULL;
		ctx->cnt = 0;
		ctx->return_code = CALLBACK_ERROR_NOCONNECTED;
		ctx->flags = FI_SEND;
		handle_callback(ctx);
		return ;
	}

again:
#if _NO_INV_RKEY
	ret = fi_sendv(ctx->ep->ep, ctx->iov, ctx->chunk_desc, ctx->cnt, 0, ctx);
#else
	if (((struct rpc_rdma_cbc *)(ctx->cb_arg))->invalidate_rkey == 0) {
		ret = fi_sendv(ctx->ep->ep, ctx->iov, ctx->chunk_desc, ctx->cnt, 0, ctx);
	} else {
		ret = fi_sendv_with_inv_rkey(ctx->ep->ep, ctx->iov, ctx->chunk_desc, ctx->cnt, 0, ctx, ((struct rpc_rdma_cbc *)(ctx->cb_arg))->invalidate_rkey);
	}
#endif
	ctx->ep->fi_send_count ++;
	if ((ret == -EAGAIN) && (cnt < 64)) {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
			"func %s,() fi sendv again reslut %d. try cnt %d.\n", __func__, ret, cnt);
		cnt ++;
	    put_cq_event(ctx->ep->domain->fab_clas);
        goto again;
	}
	if (ret) {
		ctx->return_code = CALLBACK_ERROR_FI_SENDV_FAILED;
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s:%d, fi_sendv() ret=%d (%s). \n", __func__,__LINE__, 
					(int) (ret), 
					fi_strerror((int) -(ret)) );
		handle_callback(ctx);
		//MSG_PRINTERR("fi_read", ret);
	}
	ctx->ep->send_wait_cq_count ++;
    return;
	//probe_timespec_end(ctx->ep->xd, &(ctx->start_sendv_handle_time), RPC_fabric_send_handle);
	//probe_timespec_begin(ctx->ep->xd, &(ctx->start_sendv_cq_time), RPC_fabric_s_cq);
}

int fabric_sendv(void *fd, int iov_cnt, struct iovec *iov, void *callback_arg, fabric_rpc_callback callback_func)
{
	int i, ret;
	struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
	probe_count_inc(PROBE_COUNT_FAB_CTX);

	struct fabric_endpoint *fab_endp = (struct fabric_endpoint*)fd;
	if (fab_endp->state != EP_STATE_CONNECTED) {
		//MSG_PRINTERR("connect state error",0);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s,() fab_endp %p no connected state, fab_endp->state %d.\n", __func__,fab_endp, fab_endp->state);
		ctx->ep = fab_endp;
		//ctx->iov = NULL;
		ctx->iov = iov;
		ctx->cnt = iov_cnt;
		ctx->cb_arg = callback_arg;
		ctx->cb = callback_func;
		ctx->return_code = CALLBACK_ERROR_NOCONNECTED;
		ctx->flags = FI_SEND;
		handle_callback(ctx);
		return -1;
	}

	for (i = 0; i < iov_cnt; i ++) {
		ctx->chunk_desc[i] = fab_endp->gane_mr->mem_desc;
	}

	ctx->ep = fab_endp;
	ctx->iov = iov;
	ctx->cnt = iov_cnt;
	ctx->cb_arg = callback_arg;
	ctx->cb = callback_func;
	ctx->return_code = CALLBACK_SUCEESS;
	probe_timespec_begin(ctx->ep->xd, &(ctx->start_sendv_handle_cb_time), RPC_fabric_send_handle_cb);
	//probe_timespec_begin(ctx->ep->xd, &(ctx->start_sendv_cq_time), RPC_fabric_s_cq);

	ret =  fabric_cqpool_submit(fab_clas, handle_sendv, ctx, fab_endp->thrid);
	return ret;
}

int fabric_readv(void *fd, struct xdr_read_list *readlist, void *callback_arg, fabric_rpc_callback callback_func)
{
	int ret = FI_SUCCESS;
	int rma_num = 0;
	size_t total_len = 0;
	bool mem_break = false;

	struct fabric_endpoint *fab_endp = (struct fabric_endpoint*)fd;
	if (fab_endp->state != EP_STATE_CONNECTED) {
		//MSG_PRINTERR("connect state error",0);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s,() fab_endp %p no connected state, fab_endp->state %d.\n", __func__,fab_endp, fab_endp->state);
		struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
		probe_count_inc(PROBE_COUNT_FAB_CTX);
		ctx->ep = fab_endp;
		ctx->iov = NULL;
		ctx->cnt = 0;
		ctx->cb_arg = callback_arg;
		ctx->cb = callback_func;
		ctx->return_code = CALLBACK_ERROR_NOCONNECTED;
		ctx->flags = FI_READ;
		handle_callback(ctx);
		//ret =  fabric_txpool_submit(fab_clas, handle_callback, ctx);
		return -1;
	}

	struct xdr_read_list *read_chunk = readlist;
	struct fi_rma_iov rma_iov[MAX_IOVECS];

	void* chunk_desc[MAX_IOVECS];
	struct iovec *chunk_iov = (struct iovec *)malloc(MAX_IOVECS * sizeof(struct iovec));
	probe_count_inc(PROBE_COUNT_FAB_IOV);

        while (ntohl(read_chunk->present) != 0) {
                size_t read_len =0;
                size_t left_len = 0;
                size_t offset = 0;

                //left_len = ntohl(read_chunk->target.length);
                left_len = read_chunk->target.length;
                while (left_len > 0) {
                        read_len = left_len;
                        if (left_len > fab_endp->mpool->attr->chunk_size) {
                                read_len = fab_endp->mpool->attr->chunk_size;
                        }
                        //msg
                        chunk* ck = get_chunk(fab_endp->mpool, read_len);
			if (ck == NULL) {
				mem_break = true;
				__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s,() fab_endp %p get_chunk faild. successd:%d, faild:1.\n", __func__,fab_endp, rma_num);
				break;
			}
			
                        chunk_iov[rma_num].iov_base = (void*)ck;
                        chunk_iov[rma_num].iov_len = read_len;
                        chunk_desc[rma_num] = fab_endp->pool_mr->mem_desc;

                        //rma_iov[rma_num].addr = ntohl64(read_chunk->target.offset) + offset;
                        rma_iov[rma_num].addr = read_chunk->target.offset + offset;
                        rma_iov[rma_num].len = read_len;
                        //rma_iov[rma_num].key = ntohl(read_chunk->target.handle);
                        rma_iov[rma_num].key = read_chunk->target.handle;

			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"func %s,() get chunk[%d] addr=%p, len=%d.\n", __func__, rma_num, ((chunk*)chunk_iov[rma_num].iov_base)->buffer, chunk_iov[rma_num].iov_len);
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"func %s,()       rma[%d] addr=%p, len=%d.\n", __func__, rma_num, rma_iov[rma_num].addr, rma_iov[rma_num].len);
                        rma_num++;
                        left_len -= read_len;
                        offset += read_len;
                }
		if (mem_break == true) {
			break;
		}
                read_chunk = (struct xdr_read_list *)((char *)read_chunk + sizeof(struct xdr_read_list));
                //total_len += ntohl(read_chunk->target.length);
                total_len += read_chunk->target.length;
        }

	if (mem_break == true) {
		//MSG_PRINTERR("connect state error",0);
		//__warnx(TIRPC_DEBUG_FLAG_ERROR,
		//		"func %s,() get chunk error.\n", __func__);
		struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
		probe_count_inc(PROBE_COUNT_FAB_CTX);
		ctx->ep = fab_endp;
		ctx->iov = NULL;
		ctx->cnt = 0;
		ctx->cb_arg = callback_arg;
		ctx->cb = callback_func;
		ctx->return_code = CALLBACK_ERROR_FI_READV_NOMEM;
		ctx->flags = FI_READ;
		handle_callback(ctx);
		return -1;
	}
	struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
	probe_count_inc(PROBE_COUNT_FAB_CTX);
	ctx->ep = fab_endp;
	ctx->iov = chunk_iov;
	ctx->cnt = rma_num;
	ctx->cb_arg = callback_arg;
	ctx->cb = callback_func;
	ctx->return_code = CALLBACK_SUCEESS;

	int i;
	for (i = 0; i < rma_num; i++) {
		void *context = (i == rma_num -1) ? ctx : NULL;
		int cnt = 0;	
again:
	#if 0
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s,()  fi_read  i:%d,buffer:%p, len:%d, desc:%p, rma_addr:%p, rma_key:%p. \n", __func__, i,
					((chunk*)chunk_iov[i].iov_base)->buffer,chunk_iov[i].iov_len,  chunk_desc[i], 
					rma_iov[i].addr, rma_iov[i].key);
	#endif
		ret = fi_read(fab_endp->ep, ((chunk*)chunk_iov[i].iov_base)->buffer, chunk_iov[i].iov_len, chunk_desc[i], 0, rma_iov[i].addr, rma_iov[i].key, context);
		fab_endp->fi_read_count ++;
		if ((ret == -EAGAIN) && (cnt < 64)) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"func %s,() fi read again reslut %d. try cnt %d.\n", __func__, ret, cnt);
			cnt ++;
			put_cq_event(fab_endp->domain->fab_clas);
				goto again;
		}
        	if (ret) {
			ctx->return_code = CALLBACK_ERROR_FI_READ_FAILED;
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s:%d, fi_read() ret=%d (%s). \n", __func__,__LINE__, 
					(int) (ret), 
					fi_strerror((int) -(ret)) );
			handle_callback(ctx);
            	//MSG_PRINTERR("fi_read", ret);
            	return ret;
        	}
	}

	return ret;
}

void handle_write(void *context)
{
	int ret = 0;
	int cnt = 0;
	struct general_context *ctx = (struct general_context *)context;
	void *cb_arg = ctx->cb_arg;
	probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_handle_cb_time), RPC_fabric_write_handle_cb);
	probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_cq_time), RPC_fabric_w_cq);	
	//probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_handle_time), RPC_fabric_write_handle);
again:
	ret = fi_writev(ctx->ep->ep, ctx->iov, ctx->chunk_desc, ctx->cnt, 0, ctx->rma_iov[0].addr, ctx->rma_iov[0].key, (void *)ctx);
	ctx->ep->fi_write_count ++;
	if ((ret == -EAGAIN) && (cnt < 64)) {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
			"func %s,() fi writev again reslut %d. try cnt %d.\n", __func__, ret, cnt);
		cnt ++;
	    	put_cq_event(ctx->ep->domain->fab_clas);
		goto again;
	}
	if (ret) {
		ctx->return_code = CALLBACK_ERROR_FI_WRITEV_FAILED;
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s:%d, fi_writev() ret=%d (%s). \n", __func__,__LINE__, 
					(int) (ret), 
					fi_strerror((int) -(ret)) );
		handle_callback(ctx);
		//MSG_PRINTERR("fi_write", ret);
	}
	rpc_fabric_send(cb_arg);
    return;
	//probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_handle_time), RPC_fabric_write_handle);
	//probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_cq_time), RPC_fabric_w_cq);
}
void handle_writev(void *context)
{
	int ret = 0;
	struct general_context *ctx = (struct general_context *)context;
	void *cb_arg = ctx->cb_arg;
	//probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_handle_time), RPC_fabric_write_handle);
	assert(ctx->cnt == ctx->rma_num);
	int i;
	for (i = 0; i < ctx->cnt; i ++) {
		int cnt = 0;
		assert(ctx->iov[i].iov_len == ctx->rma_iov[i].len);
		void *context = (i == ctx->cnt -1) ? ctx : NULL;
again:
		ret = fi_write(ctx->ep->ep, ctx->iov[i].iov_base, ctx->iov[i].iov_len, ctx->chunk_desc[i], 0, ctx->rma_iov[i].addr, ctx->rma_iov[i].key, context);
		if ((ret == -EAGAIN) && (cnt < 64)) {
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"func %s,() fi writev again reslut %d. try cnt %d.\n", __func__, ret, cnt);
			cnt ++;
			put_cq_event(ctx->ep->domain->fab_clas);
			goto again;
		}
		if (ret) {
			ctx->return_code = CALLBACK_ERROR_FI_WRITE_FAILED;
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"func %s:%d, fi_writev() ret=%d (%s). \n", __func__,__LINE__, 
					(int) (ret), 
					fi_strerror((int) -(ret)) );
			handle_callback(ctx);
			//MSG_PRINTERR("fi_write", ret);
		}
    }
	rpc_fabric_send(cb_arg);
    return;
	//probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_handle_time), RPC_fabric_write_handle);
}

int fabric_writev(void *fd, struct xdr_write_list *writelist, int iov_cnt, struct iovec *iov, void *callback_arg, fabric_rpc_callback callback_func, int list_size)
{
	int ret = FI_SUCCESS;
	int i, j;
	int rma_num = 0;
	struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
	if (ctx == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s NFS/FABRIC malloc ctx failed", __func__);
		return -1;
	}
	probe_count_inc(PROBE_COUNT_FAB_CTX);

	struct fabric_endpoint *fab_endp = (struct fabric_endpoint*)fd;
	if (fab_endp->state != EP_STATE_CONNECTED) {
		//MSG_PRINTERR("connect state error",0);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"func %s,() fab_endp %p no connected state, fab_endp->state %d.\n", __func__,fab_endp, fab_endp->state);
		//struct general_context *ctx = (struct general_context*)malloc(sizeof(struct general_context));
		//probe_count_inc(PROBE_COUNT_FAB_CTX);
		ctx->ep = fab_endp;
		ctx->iov = NULL;
		ctx->cnt = iov_cnt;
		ctx->cb_arg = callback_arg;
		ctx->cb = callback_func;
		ctx->return_code = CALLBACK_ERROR_NOCONNECTED;
		ctx->flags = FI_WRITE;
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s NFS/FABRIC fabric_writev is error,  cbc callback_arg %p, fab_endp:%p .", __func__, callback_arg, fab_endp);
		handle_callback(ctx);
		return -1;
	}

	for (i = 0; i < iov_cnt; ++i) {
		ctx->chunk_desc[i] = fab_endp->gane_mr->mem_desc;
	}

	//rma
	struct xdr_write_list *write_chunk = writelist;
	for(i = 0; i < list_size; ++i) {
		for(j = 0; j < write_chunk->elements; ++j) {
			ctx->rma_iov[rma_num].addr = write_chunk->entry[j].target.offset;
			ctx->rma_iov[rma_num].len = write_chunk->entry[j].target.length;
			ctx->rma_iov[rma_num].key = write_chunk->entry[j].target.handle;
			rma_num ++;
		}
		write_chunk = (struct xdr_write_list *)((char *)write_chunk + sizeof(struct xdr_write_list) + sizeof(struct xdr_write_chunk)*write_chunk->elements);
	}
	ctx->rma_num = rma_num;
	ctx->ep = fab_endp;
	ctx->iov = iov;
	ctx->cnt = iov_cnt;
	ctx->cb_arg = callback_arg;
	ctx->cb = callback_func;
	ctx->return_code = CALLBACK_SUCEESS;
	probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_handle_cb_time), RPC_fabric_write_handle_cb);
	if (ctx->rma_num == 1) {
		//probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_handle_cb_time), RPC_fabric_write_handle_cb);
		//probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_cq_time), RPC_fabric_w_cq);
		ret =  fabric_cqpool_submit(fab_clas, handle_write, ctx, fab_endp->thrid);
	} else {
		//probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_handle_cb_time), RPC_fabric_write_handle_cb);
		ret =  fabric_cqpool_submit(fab_clas, handle_writev, ctx, fab_endp->thrid);
	}
	return ret;
}

int fabric_accept(fabric_endpoint **fabric_ep, fabric_class *fab_clas, struct fi_info *info)
{
	int ret = FI_SUCCESS;
	fabric_endpoint *fab_ep = NULL;
	char local_ipstring[SOCK_NAME_MAX];
	char peer_ipstring[SOCK_NAME_MAX];
	local_ipstring[SOCK_NAME_MAX -1] = '\0';
	peer_ipstring[SOCK_NAME_MAX -1] = '\0';

	fab_ep = fabric_open_ep(fab_clas, info);
	if (fab_ep == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_open_ep error.", 
					__func__);
		return -1;
	}else {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC fabric accept success, fabric_endpoint %p.", 
					__func__, fab_ep);
	}

	struct sockaddr_storage *addr = &(fab_ep->peer_addr);
	size_t addrlen = sizeof(struct sockaddr_storage);
	fi_getpeer(fab_ep->ep, addr, &addrlen);
	sockaddr_to_ip_port(addr, peer_ipstring, sizeof(peer_ipstring));
	printf(" accepted peer  IP: %s \n", peer_ipstring);

	addr = &(fab_ep->local_addr);
	fi_getname(&(fab_ep->ep->fid), addr, &addrlen);
	sockaddr_to_ip_port(addr, local_ipstring, sizeof(local_ipstring));
	printf(" accepted local IP: %s \n", local_ipstring);

	*fabric_ep = fab_ep;
	return ret;
}

int fabric_connect(fabric_endpoint *fab_endp)
{
	int ret = FI_SUCCESS;

	ret = fi_connect(fab_endp->ep, fab_endp->fi->dest_addr, NULL, 0);
	if (ret) {
		MSG_PRINTERR("fi_connect", ret);
		return ret;
	}

	return ret;
}

#if 0
//未修改完，暂时屏蔽，目前不使用fabric_poll_start
int fabric_poll_start(fabric_class *fab_clas)
{
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if (fab_clas->domains[i]) {
			//创建poll线程
			fab_clas->stop = 0;

			// rx poll thread
			int ret = pthread_create(&fab_clas->domains[i]->poll_cq_thread, NULL, fabric_event_process, (void*)fab_clas);
			if (ret) {
				MSG_PRINTERR("pthread_create", ret);
				return ret;
			}

			pthread_setname_np(fab_clas->domains[i]->poll_cq_thread, "poll_rx_cq");
			pthread_detach(fab_clas->domains[i]->poll_cq_thread);
		}
	}

	// tx poll thread
	/*ret = pthread_create(&fab_clas->domain->tx_poll_thread, NULL, tx_fabric_process, (void*)fab_clas);
	if (ret) {
		MSG_PRINTERR("pthread_create", ret);
		return ret;
	}

	pthread_setname_np(fab_clas->domain->tx_poll_thread, "poll_tx_cq");
	pthread_detach(fab_clas->domain->tx_poll_thread);*/

	return FI_SUCCESS;
}
#endif

int fabric_poll_stop(struct fabric_domain *domain)
{
	void *retval = NULL;

	fab_clas->stop = 1;

	// rx poll thread
	int ret = pthread_join(domain->poll_cq_thread, &retval);
	//assert(ret == 0);

	// tx poll thread
	/*ret = pthread_join(domain->tx_poll_thread, &retval);
	assert(ret == 0);*/

	return ret;
}
void cq_thread_callback(void *context){

	(void)fabric_event_process(context);

	return;
}
/*
 * rpc thread callback
 * */
void handle_callback(void *context)
{
	int ret;
	struct general_context *ctx = (struct general_context *)context;
	if (ctx->return_code == CALLBACK_SUCEESS) {
		if (ctx->flags & FI_RECV) {
			probe_timespec_end(ctx->ep->xd, &(ctx->recv_cq_time), RPC_fabric_recv_cq_cb);
			(void)atomic_dec_uint64_t(&ctx->ep->credits);	
			ret = fabric_recv(ctx->ep, 1);
			ctx->iov->iov_len = ctx->len;
			ctx->ep->fi_recv_cqcallback_success_count ++;
		__warnx_rdma(TIRPC_DEBUG_FLAG_XDR_RDMA, "func %s() NFS/FABRIC fabric %p, recv ref_count %d.\n", __func__, ctx->ep, ctx->ep->ref_count);
		} else if (ctx->flags & FI_SEND) {
			ctx->ep->fi_send_cqcallback_success_count ++;
			ctx->ep->send_wait_cq_count --;
		} else if (ctx->flags & FI_READ) {
			ctx->ep->fi_read_cqcallback_success_count ++;
		} else if (ctx->flags & FI_WRITE) {
			ctx->ep->fi_write_cqcallback_success_count ++;
		} else {
			__warnx_rdma(TIRPC_DEBUG_FLAG_XDR_RDMA, "func %s() NFS/FABRIC fabric %p, flags erro %d.\n", __func__, ctx->ep, ctx->flags);
		}
	}else if (ctx->return_code == CALLBACK_FAILED ){
		if (ctx->flags & FI_RECV) {
			//if (ctx->ep->state != EP_STATE_CLOSED) {
			//	ret = fabric_recv(ctx->ep, 1);
			//}
			(void)atomic_dec_uint64_t(&ctx->ep->credits);	
			ctx->ep->fi_recv_cqcallback_failed_count ++;
		} else if (ctx->flags & FI_SEND) {
			ctx->ep->fi_send_cqcallback_failed_count ++;
		} else if (ctx->flags & FI_READ) {
			ctx->ep->fi_read_cqcallback_failed_count ++;
		} else if (ctx->flags & FI_WRITE) {
			ctx->ep->fi_write_cqcallback_failed_count ++;
		}
	}

#if 0
	if (atomic_fetch_uint64_t(&(ctx->ep->credits)) == 0) {
		__warnx_rdma(TIRPC_DEBUG_FLAG_ERROR, "func %s,() fabric %p, credits is 0, ref_count %d fabric need shutdown.\n", __func__, ctx->ep, ctx->ep->ref_count);
		(void)fabric_cqpool_submit(fab_clas, handle_shutdown, ctx->ep, 0);
	}
#endif

#if 1
	if ((ctx->return_code == CALLBACK_SUCEESS) && (wait_destory_ep_num >= wait_destory_ep_num_max) ) {
		ctx->return_code = CALLBACK_ERROR_WAITDESTORY_LIMIT;
		//assert(0);
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"func %s,() waitting destory eps is %d.\n", __func__, wait_destory_ep_num);
		if (wait_destory_ep_num >= wait_destory_ep_num_max_abort) {

			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"func %s,() waitting destory eps is %d. assert() \n", __func__, wait_destory_ep_num);
			//assert(0);
		}
	}
#endif
	assert(ctx->cb);
	ret = ctx->cb(ctx->ep, ctx->cb_arg, ctx->iov, ctx->cnt, ctx->return_code);
	if (ret) {
		MSG_PRINTERR("cb", ret);
	}

	if (ctx->flags & FI_RECV || ctx->flags & FI_READ) {
		free(ctx->iov);
		probe_count_dec(PROBE_COUNT_FAB_IOV);
	}
	if (ctx->flags & FI_SEND) {
		probe_count_dec(PROBE_COUNT_FAB_CH);
	}
	free(ctx);
	probe_count_dec(PROBE_COUNT_FAB_CTX);
}

int fabric_eq_readerr(struct fid_eq *eq)
{
	struct fi_eq_err_entry err;
	ssize_t ret;

	memset(&err, 0, sizeof(err));
	ret = fi_eq_readerr(eq, &err, 0);
	if (ret != sizeof(err)) {
		MSG_PRINTERR("fi_eq_readerr", ret);
		return ret;
	} else {
		fprintf(stderr, "Error event %d %s\n",
				err.err, fi_strerror(err.err));
		return err.err;
	}
}


int handle_eq_event(fabric_class *fab_clas, struct fid_eq *eq)
{
	int ret = FI_SUCCESS;
	uint32_t event = 0;
	struct fi_eq_cm_entry entry;
	char ipstring[SOCK_NAME_MAX];
	ipstring[SOCK_NAME_MAX -1] = '\0';
	fabric_endpoint *fab_ep = NULL;
	fabric_endpoint *fab_ep_find = NULL;

	ret = fi_eq_read(fab_clas->eq, &event, &entry, sizeof(entry), 0);
	if (ret == -FI_EAVAIL) {
		return fabric_eq_readerr(eq);
	} else if (ret < 0 && ret != -FI_EAGAIN) {
		MSG_PRINTERR("fi_eq_read", ret);
		return ret;
	}

	if (ret == -FI_EAGAIN) {
		return ret;
	}

	switch(event) {
		case FI_CONNREQ:
			assert(entry.fid->fclass == FI_CLASS_PEP);
			//assert(entry.fid == &fab_clas->fab_pep->pep->fid);
			struct fabric_domain *domain  = (struct fabric_domain *)entry.fid->context;
				ret = fabric_accept(&fab_ep, fab_clas, entry.info);
				if (ret) {
					__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_accept error ret %d.", __func__, ret);
					return ret;
				}
				int svc_connections = 0;
				assert(fab_ep != NULL);
				assert(fab_ep->state == EP_STATE_INIT);
				fab_ep->xd = svc_rdma_fabric_xprt_create(fab_ep);

				//修改ep的状态
				if (svc_vc_new_conn_ok() == true) {
					svc_connections = svc_get_connections();
					//svc_connections = svc_inc_connections();
					fab_ep->active = true;
				} else {
					fab_ep->active = false;
					svc_connections = svc_get_connections();
					__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric %p connect deny inactive, cur connections %d,", 
							__func__, fab_ep, cur_eps_count);
				}
				fab_ep->state = EP_STATE_CONNECTED;
				ret = fi_accept(fab_ep->ep, NULL, 0);
				if (ret) {
					__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric_accept error ret %d, fab_ep %p.", __func__, ret, fab_ep);
					return ret;
				}else {
					//struct fid_ep *fid_ep = 
					pthread_mutex_lock(&(domain->map_lock));
					/*key &fab_ep->ep, only user struct element*/
					HASH_ADD(hh, domain->ep_map, ep, sizeof(fab_ep->ep), fab_ep);
					//HASH_ADD(hh, fab_clas->domain->ep_map, ep, sizeof(fab_ep->ep), fab_ep);
					pthread_mutex_unlock(&(domain->map_lock));
					struct sockaddr_storage *addr = &(fab_ep->peer_addr);
					sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
					__warnx(TIRPC_DEBUG_FLAG_ERROR, "NFS/FABRIC [fabric_cm] fi_accept success. fab_ep:%p, fid_ep:%p. &fid_ep %p.  acive %d, total eps count %d, wait_destory_num %d, svc_connections(allxprt) %d, addr:%s.", 
						 fab_ep, fab_ep->ep, &(fab_ep->ep->fid), fab_ep->active, cur_eps_count, wait_destory_ep_num, svc_connections, ipstring);
				}
		
			break;
		case FI_CONNECTED:
			assert(entry.fid->fclass == FI_CLASS_EP);
			fab_ep = entry.fid->context;
			assert(fab_ep != NULL);

			pthread_mutex_lock(&(fab_ep->domain->map_lock));
			/*key &fab_ep->ep*/
			HASH_FIND(hh, fab_ep->domain->ep_map, &fab_ep->ep, sizeof(fab_ep->ep), fab_ep_find);
			pthread_mutex_unlock(&(fab_ep->domain->map_lock));
			assert(fab_ep == fab_ep_find);
					
			struct sockaddr_storage *addr = &(fab_ep->peer_addr);
			sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "NFS/FABRIC [fabric_cm][event] connect. fab_ep:%p, fid_ep:%p. entry.fid:%p, addr:%s.",
						 fab_ep, (fab_ep != NULL) ? fab_ep->ep : NULL, &(entry.fid), ipstring);
			break;
		case FI_SHUTDOWN:
			fab_ep = entry.fid->context;

			//pthread_mutex_lock(&(domain->map_lock));
			/*key &fab_ep->ep*/
			//HASH_FIND(hh, domain->ep_map, &fab_ep->ep, sizeof(fab_ep->ep), fab_ep_find);
			//pthread_mutex_unlock(&(domain->map_lock));
			if (fab_ep != NULL) {
				struct sockaddr_storage *addr = &(fab_ep->peer_addr);
				sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "NFS/FABRIC [fabric_cm][event] shutdown. fab_ep:%p, fid_ep:%p. entry.fid:%p. addr:%s. ",
							fab_ep, fab_ep->ep,  &(entry.fid), ipstring);
				fabric_shutdown(fab_ep);
			} else {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "NFS/FABRIC [fabric_cm][event] shutdown. fab_ep: NULL. ");
			
			}
			break;
		default:
			fab_ep = entry.fid->context;
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "NFS/FABRIC [fabric_cm][event] fab_ep:%p, fid_ep:%p. eq event=%d.",  
						fab_ep, (fab_ep != NULL) ? fab_ep->ep : NULL, event);

			ret = -FI_EOTHER;
			break;
	}

	return ret;
}
void fabric_shutdown(fabric_endpoint *fab_ep) {
	struct fabric_domain *domain = fab_ep->domain;
	if (fab_ep->state != EP_STATE_CLOSED) {
		fi_close(&fab_ep->ep->fid);
		//assert(fab_ep == fab_ep_find);
		fab_ep->state = EP_STATE_CLOSED;
		pthread_mutex_lock(&(domain->map_lock));
		HASH_DEL(domain->ep_map, fab_ep);
		HASH_ADD(hh, domain->ep_destory_map, ep->fid, sizeof(fab_ep->ep->fid), fab_ep);
		wait_destory_ep_num ++;
		pthread_mutex_unlock(&(domain->map_lock));

		probe_count_inc(PROBE_COUNT_WAITDESTORY);
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fi_shutdown event fab_ep %p.! wait_destory_num %d, state=%d, ref_count=%ld", 
				__func__, fab_ep, wait_destory_ep_num, fab_ep->state, fab_ep->ref_count);
	}	
	return;
}
int handle_cq_event(fabric_class *fab_clas, struct fid_cq *cq, int is_tx)
{
	int ret = FI_SUCCESS;
    int cqe_num = 8;
    if (is_tx) {
        cqe_num = 8;
    }
	struct fi_cq_msg_entry entries[512];
	ret = fi_cq_read(cq, &entries, cqe_num);

	if (ret > 0) {
		for (int i = 0; i < ret; i++) {
			if(entries[i].op_context == NULL) {
				continue;
			}
			struct general_context *ctx = (struct general_context *)(entries[i].op_context);
			ctx->flags = entries[i].flags;
			ctx->len = entries[i].len;
			if (ctx->flags & FI_RECV) {
				probe_timespec_begin(ctx->ep->xd, &(ctx->recv_cq_time), RPC_fabric_recv_cq_cb);
            } else if (ctx->flags & FI_READ) {
				probe_timespec_end(ctx->ep->xd, &(((struct rpc_rdma_cbc *)ctx->cb_arg)->start_readv_cq_time), RPC_fabric_r_cq);
			} else if (ctx->flags & FI_WRITE) {
				probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_cq_time), RPC_fabric_w_cq);
			} else if (ctx->flags & FI_SEND) {
				probe_timespec_end(ctx->ep->xd, &(ctx->start_sendv_cq_time), RPC_fabric_s_cq);
			}
			(void)fabric_cqpool_submit(fab_clas, handle_callback, ctx, ctx->ep->thrid);
	    }
	} else{
		//if (ret == -FI_EAVAIL) {
			struct fi_cq_err_entry cq_err;
			memset(&cq_err, 0, sizeof(cq_err));
			if (ret != -FI_EAGAIN)
				ret = fi_cq_readerr(cq, &cq_err, 0);
			if (ret < 0) {/*cq reader error*/ 
				if (ret != -FI_EAGAIN) {
					__warnx(TIRPC_DEBUG_FLAG_ERROR,
						"func %s:%d, () cq readerr err ret=%d (%s). \n", __func__,__LINE__, 
						(int) (ret), 
						fi_strerror((int) -(ret)) );	
				}

			} else {
				//FT_CQ_ERR(cq, cq_err, NULL, 0);
				struct general_context *ctx = (struct general_context *)(cq_err.op_context);
                		if (ctx != NULL) {
					if (ctx->flags & FI_READ) {
						if (ctx->cb_arg != NULL) {
							probe_timespec_end(ctx->ep->xd, &(((struct rpc_rdma_cbc *)ctx->cb_arg)->start_readv_cq_time), RPC_fabric_r_cq);
						}
						//probe_timespec_begin(ctx->ep->xd, &(ctx->start_readv_cq_cb_time), RPC_fabric_r_cq_cb);
					} else if (ctx->flags & FI_WRITE) {
						probe_timespec_end(ctx->ep->xd, &(ctx->start_writev_cq_time), RPC_fabric_w_cq);
						//probe_timespec_begin(ctx->ep->xd, &(ctx->start_writev_cq_cb_time), RPC_fabric_w_cq_cb);
					} else if (ctx->flags & FI_SEND) {
						probe_timespec_end(ctx->ep->xd, &(ctx->start_sendv_cq_time), RPC_fabric_s_cq);
						//probe_timespec_begin(ctx->ep->xd, &(ctx->start_sendv_cq_cb_time), RPC_fabric_s_cq_cb);
					}
					ctx->flags = cq_err.flags;
					ctx->len = cq_err.len;
					ctx->return_code = CALLBACK_FAILED;

#if 1
					struct sockaddr_storage *addr = &(ctx->ep->peer_addr);
					char ipstring[SOCK_NAME_MAX];
					ipstring[SOCK_NAME_MAX -1] = '\0';
					sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
					__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
						"func %s:%d, ctx->ep:%p, () cq readerr err ret=%d, (%d)(%s), provider errno: %d, addr:%s. \n", __func__,__LINE__,ctx->ep,ret ,
						cq_err.err,
						 fi_strerror(cq_err.err), 
						 cq_err.prov_errno, ipstring);
#endif
					//(void)fabric_rxpool_submit(fab_clas, handle_callback, ctx);
					(void)fabric_cqpool_submit(fab_clas, handle_callback, ctx, ctx->ep->thrid);
				}
			}
		//} else {
			//MSG_PRINTERR("fi_cq_read", ret);
		//}
	}

	return ret;
}

int put_cq_event(fabric_class* fabric)
{
    __warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRI", __func__);
    int ret = 0;
    //int tx_event;
    struct fabric_domain *domain;
    //struct fid *fids[1];
	int thread_id = thpool_get_curthreadid();
#if 0
    fids[0] = &domain->tx_cq[thread_id]->fid;
    tx_event = fi_trywait(fab_clas->fabric, fids, 1);
    if (tx_event != FI_SUCCESS) {
        ret = handle_cq_event(fab_clas, domain->tx_cq[thread_id], 1);
	    __warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRI put cq event success %d.", __func__, ret);
    }
#endif
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if(fab_clas->domains[i]){
			domain = fab_clas->domains[i];
			handle_cq_event(fab_clas, domain->tx_cq[thread_id], 1);
		}
	}
    __warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRI put cq event success %d.", __func__, ret);
    return ret;
}
static inline int  handle_cq_read(fabric_class *fab_clas, int thr_index, uint64_t *poll_count_tx, uint64_t *poll_count_rx, bool *isbusy) {


	struct fabric_domain *domain;
	//int tx_event;
	//int rx_event;
	//struct fid *fids[1];
	int ret = 0;

#if 0
	fids[0] = &domain->tx_cq[thr_index]->fid;
	tx_event = fi_trywait(fab_clas->fabric, fids, 1);
	if (tx_event != FI_SUCCESS) {
		ret = handle_cq_event(fab_clas, domain->tx_cq[thr_index], 1);
		if (ret) {
			//MSG_PRINTERR("handle_cq_event", ret);
		}
	}

	fids[0] = &domain->rx_cq[thr_index]->fid;
	rx_event = fi_trywait(fab_clas->fabric, fids, 1);
	if (rx_event != FI_SUCCESS) {
		ret = handle_cq_event(fab_clas, domain->rx_cq[thr_index], 0);
		if (ret) {
			//MSG_PRINTERR("handle_cq_event", ret);
		}
	}
	return (tx_event | rx_event);
#endif
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if(fab_clas->domains[i]){
			domain = fab_clas->domains[i];
			if(handle_cq_event(fab_clas, domain->tx_cq[thr_index], 1) > 0){
				(*poll_count_tx) ++;
				(*isbusy) = true;
				ret = 1;
			}
			if(handle_cq_event(fab_clas, domain->rx_cq[thr_index], 0) > 0){
				(*poll_count_rx) ++;
				(*isbusy) = true;
				ret = 1;
			}
		}
	}
	return ret;
}
static inline void handle_txtask_pop(fabric_class *fab_clas, int thr_index, uint64_t *poll_count_have, bool *isbusy){

	if (fab_clas->cq_thpool != NULL) {
		if(thread_queue_pop_nolock(fab_clas->cq_thpool, thr_index)){
			(*poll_count_have) ++;
			(*isbusy) = true;
		}
	}

}


void *fabric_event_process(void *fabric)
{
	//int ret = 0;
	//int eq_event;
	fabric_class *fab_clas = (fabric_class*)fabric;
	int thread_id = thpool_get_curthreadid();
	uint64_t poll_count_total = 0;
	uint64_t poll_count_tx = 0;
	uint64_t poll_count_rx = 0;
	uint64_t poll_count_event = 0;
	uint64_t poll_count_have = 0;
	uint64_t totaltime=0, busytime=0;
	uint64_t i = 0;

    if (cq_cpu >= 0) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        CPU_SET(cq_cpu, &cpuset);
        pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset);
	    __warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC cq_thread id %d, setaffinity %d .", __func__, thread_id, cq_cpu);
	}


	while (!fab_clas->stop) {
		i++;
		bool isbusy = false;
		struct timespec begin, end;
		poll_count_total ++;
		if (fabric_get_ep_num(fab_clas, thread_id) == 0) {
			struct timespec ts = {
				.tv_sec = 0,
				.tv_nsec = 50000,
			};
			nanosleep(&ts, NULL);
		}
		if (enable_debug_cq) {
			clock_gettime(CLOCK_REALTIME, &begin);
		}
		//ret = handle_cq_read(fab_clas, thread_id, &poll_count_tx, &poll_count_rx, &isbusy);
		handle_cq_read(fab_clas, thread_id, &poll_count_tx, &poll_count_rx, &isbusy);
		/* only first cq thread call eq func connect disconnect */
		if (thread_id == 0) {
			if (i  > 5000 && fab_clas->eq != NULL) {
				handle_eq_event(fab_clas, fab_clas->eq);
				i = 0;
			}
		}
		handle_txtask_pop(fab_clas, thread_id, &poll_count_have, &isbusy);
		if(isbusy == true){
			poll_count_event ++;
			if (enable_debug_cq) {
				clock_gettime(CLOCK_REALTIME, &end);
				busytime += timespec_diff(&end, &begin);
			}
		}
		if (poll_count_total%1000000 == 0) {
			if (enable_debug_cq) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, 
					"%s() NFS/FABRIC cq_thread id %d, poll_count_total %llu, poll_count_rx %llu,"
					"poll_count_tx %llu, poll_count_have %llu,  poll_count_event %llu", __func__, 
					thread_id, poll_count_total, poll_count_rx, poll_count_tx, poll_count_have, poll_count_event);
			}
			poll_count_total = 0;
			poll_count_tx = 0;
			poll_count_rx = 0;
			poll_count_event = 0;
			poll_count_have = 0;
		}

		//if (domain->ep_destory_map != NULL && thread_id == 0 && ret == FI_SUCCESS ) {
		int index;
		for (index = 0; index < fab_clas->domain_count; index++) {
			if(fab_clas->domains[index]){
				destory_map_process(fab_clas, fab_clas->domains[index], thread_id);
			}
		}
		
		if (enable_debug_cq){
			clock_gettime(CLOCK_REALTIME, &end);
			totaltime += timespec_diff(&end, &begin);

			if (totaltime >= 1*1000*1000*1000) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() cq_thread id %d, usage: %f%%", __func__, thread_id,  busytime/(double)totaltime * 100);
				totaltime = busytime = 0;
			}
		}
	}
	return NULL;
}
void destory_fab_ep(fabric_endpoint *fab_ep) {
	struct sockaddr_storage *addr = &(fab_ep->peer_addr);
	char ipstring[SOCK_NAME_MAX];
	ipstring[SOCK_NAME_MAX -1] = '\0';
	sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC fabric free fabric endpoint %p success! addr:%s", 
		__func__, fab_ep, ipstring);
	fabric_del_ep_list(fab_ep);
        fi_freeinfo(fab_ep->fi);
        free(fab_ep);
}
int destory_domain(struct fabric_domain *domain)
{
    int ret = FI_SUCCESS;

	//destory_all_endpoint(domain->ep_destory_map);

	destory_all_endpoint(domain);
	free(domain->rx_cq);
	free(domain->tx_cq);
	fi_close(&domain->pool_mr->fid);
	fi_close(&domain->gane_mr->fid);
	fi_close(&domain->fi_domain->fid);
	mpool_free(domain->mpool);
#if 0
    mpool_free(domain->gane_mpool);
#endif
    free(domain);
    return ret;
}

extern uint64_t FABRIC_IN_PROCESS_COUNT;

void fabric_getclients_fordomain(char **clients, int max_nums, int *client_nums, struct fabric_domain *domain)
{
	fabric_endpoint *fab_ep = NULL;
	fabric_endpoint *fab_ep_tmp = NULL;
	char *str_buf = NULL;
	int count = 0;

	/*session:srcip:srcport:state:onlinetime*/

	pthread_mutex_lock(&(domain->map_lock));
	HASH_ITER(hh, domain->ep_map, fab_ep, fab_ep_tmp) {
		int offset = 0;
		if (count > (max_nums-1))
			break;
		str_buf = malloc(4096);
		if (str_buf == NULL)
			break;
		memset(str_buf, 0, 4096);
		struct sockaddr_storage *addr = &(fab_ep->peer_addr);
		char ipstring[SOCK_NAME_MAX];
		ipstring[SOCK_NAME_MAX - 1] = '\0';
		sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
		//struct fid_ep *ep = fab_ep->ep;

		sprintf(str_buf, "%4d ", fab_ep->session_id);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "%20s", ipstring);
		//offset = strlen(str_buf);
		//sprintf(str_buf + offset, ":%5d ", ntohs(addr->sin_port));
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "active:%1s ", fab_ep->active ?  "1": "0");
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "fab_ep:%p ", fab_ep);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "count:%lu ", fab_ep->ref_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "fid_ep:%p ", fab_ep->ep);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "credits:%lu ", fab_ep->credits);
		/*
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "send_c:%2lu ", fab_ep->fi_send_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "write_c:%2lu ", fab_ep->fi_write_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "read_c:%2lu ", fab_ep->fi_read_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "recv_c:%2lu ", fab_ep->fi_recv_count);

		offset = strlen(str_buf);
		sprintf(str_buf + offset, "s_cb_s:%2lu ", fab_ep->fi_send_cqcallback_success_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "w_cb_s:%2lu ", fab_ep->fi_write_cqcallback_success_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "r_cb_s:%2lu ", fab_ep->fi_read_cqcallback_success_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "rc_cb_s:%2lu ", fab_ep->fi_recv_cqcallback_success_count);

		offset = strlen(str_buf);
		sprintf(str_buf + offset, "s_cb_f:%2lu ", fab_ep->fi_send_cqcallback_failed_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "w_cb_f:%2lu ", fab_ep->fi_write_cqcallback_failed_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "r_cb_f:%2lu ", fab_ep->fi_read_cqcallback_failed_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "rc_cb_f:%2lu ", fab_ep->fi_recv_cqcallback_failed_count);
		*/
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "in_p_c:%2lu ", fab_ep->in_process_count);

		offset = strlen(str_buf);
		sprintf(str_buf + offset, "F_P_c:%2lu ", FABRIC_IN_PROCESS_COUNT);

                //offset = strlen(str_buf);
                //sprintf(str_buf + offset, "rdma_credit:%2lu ", );

		time_t seconds = fab_ep->online_time.tv_sec;
		struct tm tm_info;
		localtime_r(&seconds, &tm_info);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "%d-%d-%d-%d:%d:%d ", tm_info.tm_year + 1900, 
			tm_info.tm_mon +1 +1, tm_info.tm_mday, tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec);
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC str_buf %s ", __func__, str_buf);
		clients[count] = str_buf;
		count ++;

	}
	if(count == 0){
		str_buf = malloc(4096);
		if (str_buf != NULL) {
			memset(str_buf, 0, 4096);
			sprintf(str_buf, " no rdma clients");
			clients[0] = str_buf;
			count = 1;	
		}
	}
	*client_nums = count;
	pthread_mutex_unlock(&(domain->map_lock));
	return ;
}

void fabric_getclients(char **clients, int max_nums, int *client_nums) {
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if (fab_clas->domains[i]) {
			fabric_getclients_fordomain(clients, max_nums, client_nums, fab_clas->domains[i]);
		}
	}
}

void fabric_get_waitdel_clients_fordomain(char **clients, int max_nums, int *client_nums, struct fabric_domain *domain)
{
	fabric_endpoint *fab_ep = NULL;
	fabric_endpoint *fab_ep_tmp = NULL;
	char *str_buf = NULL;
	int count = 0;

	/*session:srcip:srcport:state:onlinetime*/

	pthread_mutex_lock(&(domain->map_lock));
	HASH_ITER(hh, domain->ep_destory_map, fab_ep, fab_ep_tmp) {
		int offset = 0;
		if (count > (max_nums-1))
			break;
		str_buf = malloc(4096);
		if (str_buf == NULL)
			break;
		memset(str_buf, 0, 4096);
		struct sockaddr_storage *addr = &(fab_ep->peer_addr);
		//struct fid_ep *ep = fab_ep->ep;
		char ipstring[SOCK_NAME_MAX];
		ipstring[SOCK_NAME_MAX - 1] = '\0';
		sockaddr_to_ip_port(addr, ipstring, sizeof(ipstring));
		sprintf(str_buf, "%4d ", fab_ep->session_id);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "%20s", ipstring);
		//offset = strlen(str_buf);
		//sprintf(str_buf + offset, ":%5d ", ntohs(addr->sin_port));
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "active:%1s ", fab_ep->active ?  "1": "0");
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "fab_ep:%p ", fab_ep);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "count:%lu ", fab_ep->ref_count);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "credits:%lu ", fab_ep->credits);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "fid_ep:%p ", fab_ep->ep);

		time_t seconds = fab_ep->online_time.tv_sec;
		struct tm tm_info;
		localtime_r(&seconds, &tm_info);
		offset = strlen(str_buf);
		sprintf(str_buf + offset, "%d-%d-%d-%d:%d:%d ", tm_info.tm_year + 1900, 
			tm_info.tm_mon +1 +1, tm_info.tm_mday, tm_info.tm_hour, tm_info.tm_min, tm_info.tm_sec);
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC str_buf %s ", __func__, str_buf);
		clients[count] = str_buf;
		count ++;

	}
	if(count == 0){
		str_buf = malloc(4096);
		if (str_buf != NULL) {
			memset(str_buf, 0, 4096);
			sprintf(str_buf, " no rdma clients");
			clients[0] = str_buf;
			count = 1;	
		}
	}
	*client_nums = count;
	pthread_mutex_unlock(&(domain->map_lock));
	return ;
}

void fabric_get_waitdel_clients(char **clients, int max_nums, int *client_nums) {
	int i;
	for (i = 0; i < fab_clas->domain_count; i++) {
		if (fab_clas->domains[i]) {
			fabric_get_waitdel_clients_fordomain(clients, max_nums, client_nums, fab_clas->domains[i]);
		}
	}
}


int fabric_add_virtual_ip(const char *vip)
{
	if (!fab_clas || !vip || !*vip)
		return -EINVAL;

	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() start listen vip:%s",  __func__, vip);
	bool delete_flag = false;
	pthread_mutex_lock(&fab_clas->pep_lock);
	/* 检查是否已经有重复IP了 */
	for (struct fabric_pep *p = fab_clas->fab_pep; p && p->pep; p = p->next){
		if (strcmp(p->ip, vip) == 0){
			//1、获取当前VIP对应的网卡名；
			char netdev[DEVSIZE] = {0};
			int ret = get_netdev_by_addr(vip, netdev);
			if (ret != 0) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() failed to get netdev for IP %s, ret=%s",  __func__, strerror(ret));
				pthread_mutex_unlock(&fab_clas->pep_lock);
				return -1;
			}
			//2、比较当前网卡名与pep中的是否一致；
			if (strncmp(netdev, p->domain->netdev, DEVSIZE) == 0) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() the vip:%s is already listen",  __func__, vip);
				pthread_mutex_unlock(&fab_clas->pep_lock);
				return 0;
			}
			delete_flag = true;
			break;
		}
	}
	
	pthread_mutex_unlock(&fab_clas->pep_lock);
	if (delete_flag) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() the vip:%s have a old listen, now we need to delete it",  __func__, vip);
		fabric_delete_virtual_ip(vip);
	}

	RDMAXPRT *xd = NULL;
	xd = rdma_xprt_class.rdmaxprt;
	while (rdma_xprt_class.rdmaxprt == NULL) {
		usleep(100000);  // 100ms
	}
	
	int ret, try_count;
	ret = fabric_creat_domain_and_start_listen(fab_clas, vip, xd);
	try_count = 1;
	while (ret != 0 && try_count <= 2) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  fabric_creat_domain_and_start_listen failed, vip_list=%s, ret=%d, try again", __func__, vip, ret);
		ret = fabric_creat_domain_and_start_listen(fab_clas, vip, xd);
		try_count++;
		sleep(1);
	} 
	if (ret != 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  fabric_creat_domain_and_start_listen failed, vip_list=%s, ret=%d", __func__, vip, ret);
		return -1;
	}
	
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() sucessfully listen vip:%s",  __func__, vip);
	
	return 0;
}

int fabric_delete_virtual_ip(const char *vip)
{
	if (!fab_clas || !vip || !*vip)
		return -EINVAL;
	
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() start delete vip:%s",  __func__, vip);
	pthread_mutex_lock(&fab_clas->pep_lock);

	// 1、找到需要删除的ip所在的 fabric_pep
	struct fabric_pep *prev = NULL;
	struct fabric_pep *cur  = fab_clas->fab_pep;
	while (cur && cur->pep) {
		if (strcmp(cur->ip, vip) == 0)
			break;
		prev = cur;
		cur  = cur->next;
	}
	if (!cur || !cur->pep) {
		pthread_mutex_unlock(&fab_clas->pep_lock);
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() the vip:%s is not listening", __func__, vip);
		return -ENOENT;
	}

	//2、关闭fid
	int ret = fi_close(&cur->pep->fid);
	if (ret) 
		MSG_PRINTERR("fi_close", ret);

	struct fabric_domain *domain;
	
	//3、拿到domain。摘链释放pep
	if (prev)
		prev->next = cur->next;
	else
		fab_clas->fab_pep = cur->next;
	domain = cur->domain;
	cur->domain = NULL;
	free(cur);

	//4、通过ip 找到domain
	/*domain = get_domain_by_ip(fab_clas, vip);
	if (domain == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() connot find domain by ip:%s", __func__, vip);
	}*/
	
	//5、从domain中删除IP
	ret = delete_ip_from_domain(fab_clas, vip, domain);
	if (ret != 0) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() delete ip:%s, from domain:%p failed", __func__, vip, domain);
	}
	pthread_mutex_unlock(&fab_clas->pep_lock);

	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() stop listening %s sucessfully", __func__, vip);
	return ret;
}


void fabric_get_listen_ips(char **ips, int max_nums, int *ip_nums)
{
    struct fabric_pep *fpep = NULL;
    int count = 0;
	int buff_len = 128;
	
    if (!fab_clas || !ips || !ip_nums) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() Invalid argument", __func__);
        return;
	}

    /* 遍历链表，收集 IP */
    for (fpep = fab_clas->fab_pep;
         fpep && fpep->pep && count < max_nums;
         fpep = fpep->next)
    {
        char *buf = malloc(buff_len);
        if (!buf){
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() malloc buf failed", __func__);
            break;                       /* 内存不足，提前结束 */
		}

        if (fpep->ip[0] != '\0')
            snprintf(buf, buff_len, "%s", fpep->ip);
        else
            snprintf(buf, buff_len, "<unknown>");

        ips[count++] = buf;
    }

    /* 若没有任何监听 IP，也返回一条提示 */
    if (count == 0 && max_nums > 0) {
        char *buf = malloc(buff_len);
        if (buf) {
            snprintf(buf, buff_len, "no listening ip");
            ips[0] = buf;
            count  = 1;
        }
    }

    *ip_nums = count;
}


